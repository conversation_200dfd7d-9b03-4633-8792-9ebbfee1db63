namespace P8SDKWeChat
{
    /// <summary>
    /// P8SDK 暴露的接口
    /// </summary>
    public static class P8SDK
    {
        /// <summary>
        /// 登录
        /// 必接，在启动游戏后调用。
        /// </summary>
        /// <param name="option"></param>
        public static void Login(LoginOption option)
        {
            P8SDKManagerHandler.Instance.Login(option);
        }

        /// <summary>
        /// SDK激活
        /// </summary>
        public static void OnActiveFunc(GeneralCallbackOption option)
        {
            P8SDKManagerHandler.Instance.OnActiveFunc(option);
        }

        /// <summary>
        /// 登录行为上报，必接
        /// </summary>
        public static void PushLoginData(PushLoginDataOption option)
        {
            P8SDKManagerHandler.Instance.PushLoginData(option);
        }

        /// <summary>
        /// 角色升级上报，必接
        /// 每次玩家升级或者过关的时候调用一下
        /// </summary>
        public static void UpgradeRecord(UpgradeRecordOption option)
        {
            P8SDKManagerHandler.Instance.UpgradeRecord(option);
        }

        /// <summary>
        /// 新创角色上报，按需接入
        /// 传入对应的请求参数
        /// 只有初次注册的时候会统计
        /// </summary>
        public static void SignLog(SignLogOption option)
        {
            P8SDKManagerHandler.Instance.SignLog(option);
        }

        /// <summary>
        /// 关卡进出上报
        /// </summary>
        /// <param name="option"></param>
        public static void LevelUpgrade(LevelUpgradeOption option)
        {
            P8SDKManagerHandler.Instance.LevelUpgrade(option);
        }

        /// <summary>
        /// 初始化广告
        /// 参数没有传空值
        /// </summary>
        /// <param name="option"></param>
        public static void WXAdInit(WXAdInitOption option)
        {
            P8SDKManagerHandler.Instance.WXAdInit(option);
        }

        /// <summary>
        /// 播放激励广告视频
        /// </summary>
        public static void VideoAdShow(VideoAdShowOption option)
        {
            P8SDKManagerHandler.Instance.VideoAdShow(option);
        }

        /// <summary>
        /// 播放插屏广告视频
        /// </summary>
        /// <param name="option"></param>
        public static void SceneAdShow(SceneAdShowOption option)
        {
            P8SDKManagerHandler.Instance.SceneAdShow(option);
        }

        /// <summary>
        /// 显示Banner广告
        /// </summary>
        public static void BannerAdShow(BannerAdShowOption option = null)
        {
            P8SDKManagerHandler.Instance.BannerAdShow(option);
        }

        /// <summary>
        /// 隐藏Banner
        /// </summary>
        public static void BannerAdHide()
        {
            P8SDKManagerHandler.Instance.BannerAdHide();
        }

        /// <summary>
        /// 显示模版广告
        /// </summary>
        public static void CustomAdShow(CustomAdShowOption option = null)
        {
            P8SDKManagerHandler.Instance.CustomAdShow(option);
        }

        /// <summary>
        /// 隐藏模版广告
        /// </summary>
        public static void CustomAdHide()
        {
            P8SDKManagerHandler.Instance.CustomAdHide();
        }

        /// <summary>
        /// 注册模版广告关闭事件
        /// </summary>
        public static void CustomAdOnClose(GeneralCompleteCallbackOption option)
        {
            P8SDKManagerHandler.Instance.CustomAdOnClose(option);
        }

        /// <summary>
        /// C#输出日志到Js控制台
        /// </summary>
        /// <param name="message"></param>
        public static void Log(string message)
        {
            P8SDKManagerHandler.Instance.LogToJs($"C# >>>> {message} <<<<");
        }
    }
}