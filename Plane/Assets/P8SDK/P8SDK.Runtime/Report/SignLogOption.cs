using System;
using UnityEngine.Scripting;

namespace P8SDKWeChat
{
    [Preserve]
    public class SignLogOption : ICallback<GeneralSuccessCallbackResult, GeneralCallbackResult>
    {
        [Preserve]
        public class LoginOrder
        {
            /// <summary>
            /// 服务器Id
            /// </summary>
            public string sid;
            
            /// <summary>
            /// 角色Id
            /// </summary>
            public string roleid;
            
            /// <summary>
            /// 角色名
            /// </summary>
            public string rolename;
            
            /// <summary>
            /// 角色等级
            /// </summary>
            public string level;
        }

        public LoginOrder loginOrder;
        public Action<GeneralSuccessCallbackResult> success { get; set; }
        public Action<GeneralCallbackResult> fail { get; set; }
    }
}