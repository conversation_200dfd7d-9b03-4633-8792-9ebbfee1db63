using System.Collections.Generic;
using Random = UnityEngine.Random;

namespace P8SDKWeChat
{
    public class P8Utils
    {
        public static string GetCallbackId<T>(Dictionary<string, T> dict)
        {
            int num = dict.Count;
            string text = ((float)num + Random.value).ToString();
            while (dict.ContainsKey(text))
            {
                num++;
                text = ((float)num + Random.value).ToString();
            }

            return text;
        }
    }
}