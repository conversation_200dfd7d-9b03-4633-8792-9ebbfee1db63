using System.Collections.Generic;
using LitJson;
using UnityEngine;

namespace P8SDK<PERSON>eChat
{
    public partial class P8SDKManagerHandler : MonoBehaviour
    {
        public const string VERSION = "1.0.8";
        
        private static P8SDKManagerHandler _instance;

        public static P8SDKManagerHandler Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new GameObject("P8SDKManagerHandler").AddComponent<P8SDKManagerHandler>();
                    DontDestroyOnLoad(_instance.gameObject);
                }
                
                return _instance;
            }
        }
        
        private Dictionary<string, GeneralCompleteCallbackOption> _generalCompleteCallbackOptions;
        private Dictionary<string, GeneralCallbackOption> _generalCallbackOptions;
        
        static P8SDKManagerHandler()
        {
            // 字符串 -> 整数
            JsonMapper.RegisterImporter<string, int>(int.Parse);
            JsonMapper.RegisterImporter<string, long>(long.Parse);
            
            // 整数 -> 字符串
            JsonMapper.RegisterImporter<int, string>(intValue => intValue.ToString());
            JsonMapper.RegisterImporter<long, string>(intValue => intValue.ToString());
        }
    }
}