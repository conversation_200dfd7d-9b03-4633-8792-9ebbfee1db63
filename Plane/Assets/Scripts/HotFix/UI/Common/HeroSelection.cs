using System;
using GameCommon;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class HeroSelection : MonoBehaviour
{
    [SerializeField] private ScrollRect heroSlidesScroll;
    [SerializeField] private HeroSlides heroSlides;
    [SerializeField] private ScrollRect heroListScroll;
    [SerializeField] private HeroList heroList;
    [SerializeField] private HeroStatusButton heroStatusButton;
    [SerializeField] private Button previewButton;
    [SerializeField] private Image equipIcon;
    [SerializeField] private TMP_Text heroName;

    public event Action<int> OnSelected;

    public int HeroRendererOrder
    {
        set => heroSlides.RendererOrder = value;
    }

    private int CurHeroId
    {
        get => _curHeroId;
        set
        {
            if (_curHeroId != value)
            {
                _curHeroId = value;
                // Debug.LogFormat("HeroSelection.set_CurHeroId: {0}", _curHeroId);
                heroStatusButton.Refresh(_curHeroId);
                var icon = EquipConfig.Instance.Get(HeroConfig.Instance.Get(CurHeroId).MainEquip[0]).Icon;
                ResourceManager.LoadSprite(equipIcon, "buff", icon, true);

                OnSelected?.Invoke(_curHeroId);
            }
            RefreshHeroName();
        }
    }

    private int _curHeroId;

    private int _curIndex;

    private void Awake()
    {
        previewButton.AddListener(OnClickPreviewButton);
        heroSlides.OnIndexSelected += OnHeroSlidesSelected;
        heroList.OnIndexSelected += OnHeroListSelected;
    }

    public void Refresh(int heroId)
    {
        int index = HeroConfig.Instance.GetList().FindIndex(cfg => cfg.ID == heroId);
        // Debug.LogFormat("HeroSelection.Refresh, {0} {1}", index, heroId);
        CurHeroId = heroId;
        _curIndex = index;

        heroSlides.SetIndex(_curIndex, false);
        heroList.SetIndex(_curIndex, false);
    }

    private void RefreshHeroName()
    {
        var level = RaisingCharData.Instance.GetHeroLevel(CurHeroId);
        if (level < 0)
            level = 0;
        heroName.text = Util.GetLocalize("raising_hero_name",
            Util.GetLocalize(HeroConfig.Instance.Get(CurHeroId).Name), level);
    }

    private void OnHeroListSelected(int index)
    {
        int heroId = HeroConfig.Instance.GetList()[index].ID;
        // Debug.LogFormat("OnHeroListSelected, {0} {1}", index, heroId);
        CurHeroId = heroId;
        _curIndex = index;
        heroSlides.SetIndex(index, false);
    }

    private void OnHeroSlidesSelected(int index)
    {
        int heroId = HeroConfig.Instance.GetList()[index].ID;
        // Debug.LogFormat("OnHeroSlidesSelected, {0} {1}", index, heroId);
        CurHeroId = heroId;
        _curIndex = index;
        heroList.SetIndex(index, false);
    }

    private void OnClickPreviewButton()
    {
        var equipId = HeroConfig.Instance.Get(CurHeroId).MainEquip[0];
        var args = new object[] { equipId, false, false };
        UIManager.Instance.OpenWindow(EWindowName.EquipRise, args: args);
    }
}