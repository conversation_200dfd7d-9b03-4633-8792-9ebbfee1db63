using GameCommon;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class CultivateGiftPop : WindowBase
{
    [SerializeField] private Image imgTitle;
    [SerializeField] private Button btnBack;
    [SerializeField] private CultivateGiftItem packFree;
    [SerializeField] private CultivateGiftItem packAd;
    [SerializeField] private TextMeshProUGUI txtID;

    private int _packId;
    private CultivateGiftCfg _cultivateGiftCfg;

    protected override void OnInit()
    {
        base.OnInit();
        btnBack.AddListener(OnClickClose);
    }


    protected override void OnOpen()
    {
        base.OnOpen();

        if (_initArgs.Length < 1) return;

        _packId = (int)_initArgs[0];
        _cultivateGiftCfg = CultivateGiftConfig.Instance.Get(_packId);

        UpdateView();
    }

    private void UpdateView()
    {
        ResourceManager.LoadSprite(imgTitle, "gift", _cultivateGiftCfg.TitleIcon, true);
        packFree.gameObject.SetActive(!string.IsNullOrEmpty(_cultivateGiftCfg.FreeReward));
        if (packFree.gameObject.activeSelf)
            packFree.Show(_packId, true, OnClaimFree);
        packAd.Show(_packId, false, OnClaimAD);

#if GAME_DEBUG
        if (txtID != null)
            txtID.text = _packId.ToString();
#else
        if (txtID != null)
            txtID.gameObject.SetActive(false);
#endif
    }

    private void OnClaimFree()
    {
        if (CultivateGiftData.Instance.ClaimPackFree(_packId))
        {
            packFree.Show(_packId, true, OnClaimFree);
            CloseIfClaimed();
        }
    }

    private void OnClaimAD()
    {
        if (TimesData.GetTimesByAdId(_cultivateGiftCfg.AdId) == 0)
            ClaimAd();
        else
            ADManager.Instance.ShowRewardAd(_cultivateGiftCfg.AdId, AdCallback);
    }

    private void AdCallback(bool success)
    {
        if (success)
        {
            if (TimesData.GetTimesByAdId(_cultivateGiftCfg.AdId) == 0)
                ClaimAd();
            else
                packAd.Show(_packId, false, OnClaimAD);
        }
    }

    private void ClaimAd()
    {
        if (CultivateGiftData.Instance.ClaimPackAd(_packId))
        {
            packFree.Show(_packId, true, OnClaimFree);
            packAd.Show(_packId, false, OnClaimAD);
            CloseIfClaimed();
        }
    }

    private void CloseIfClaimed()
    {
        if (CultivateGiftData.Instance.GetPackState(_cultivateGiftCfg.ID) == CultivateGiftData.State.Claimed)
        {
            OnClickClose();
        }
    }

    public override void OnClickClose()
    {
        base.OnClickClose();
        GlobalEvent.DispatchEvent(EGameEvent.CheckGuide);
    }
}
