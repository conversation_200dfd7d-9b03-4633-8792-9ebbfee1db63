using System;
using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using GameCommon;
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// 动画方向
/// </summary>
public enum eAniDiraction
{
    right,
    left,
}

public class NavigationWindow : WindowBase
{
    public Button btn_shop;
    public Button btn_main;
    public Button btn_battle;
    public Button btn_goldlevel;
    public Button btn_bag;
    public GameObject btn_shop_lockmask;
    public GameObject btn_main_lockmask;
    public GameObject btn_battle_lockmask;
    public GameObject btn_goldlevel_lockmask;
    public GameObject btn_bag_lockmask;

    public Image obj_shopLockMask;
    public Image obj_mainLockMask;
    public Image obj_battleLockMask;
    public Image obj_goldlevelLockMask;
    public Image obj_bagLockMask;

    public Image obj_shopSelected;
    public Image obj_mainSelected;
    public Image obj_battleSelected;
    public Image obj_goldlevelSelected;
    public Image obj_bagSelected;
    public GameObject obj_shopRedDot;
    public GameObject obj_mainRedDot;
    public GameObject obj_battleRedDot;
    public GameObject obj_bagRedDot;
    public GameObject obj_goldlevelRedDot;

    /// <summary>
    /// 用于动画时锁定按钮
    /// </summary>
    public Image lockMask;

    private RectTransform shopRectTrans;
    private RectTransform mainRectTrans;
    private RectTransform battleRectTrans;
    private RectTransform bagRectTrans;
    private RectTransform goldlevelRectTrans;

    public ETabType TabType { get; private set; }
    private ETabType previousTab = ETabType.None;

    /// <summary>
    /// 按钮列表
    /// </summary>
    private List<Button> btnList = new();
    private List<Image> selectList = new();

    /// <summary>
    /// 已经打开
    /// </summary>
    bool opend = false;

    /// <summary>
    /// 是否播放动画
    /// </summary>
    bool playAni = true;

    /// <summary>
    /// tab枚举和list里面的一一对应
    /// </summary>
    public enum ETabType
    {
        None = 0,
        Shop = 1,
        Character = 2,
        Battle = 3,
        Bag = 4,
        GoldLevel = 5,
    }

    /// <summary>
    /// 枚举对应的窗口
    /// </summary>
    Dictionary<ETabType, string> tabWindowDic = new Dictionary<ETabType, string>()
    {
        { ETabType.Shop, EWindowName.Shop },
        { ETabType.Character, EWindowName.RaisingCharacter },
        { ETabType.Battle, EWindowName.Level },
        { ETabType.Bag, EWindowName.Equip },
        { ETabType.GoldLevel, EWindowName.GoldLevelWindow },
    };

    protected override void OnInit()
    {
        base.OnInit();
        // btnList.Add(btn_shop);
        // btnList.Add(btn_main);
        // btnList.Add(btn_battle);
        // btnList.Add(btn_goldlevel);
        // btnList.Add(btn_bag);
        selectList.Add(obj_shopSelected);
        selectList.Add(obj_mainSelected);
        selectList.Add(obj_battleSelected);
        selectList.Add(obj_bagSelected);
        selectList.Add(obj_goldlevelSelected);
        btn_shop.AddListener(() => OnClickTab(ETabType.Shop));
        btn_main.AddListener(() => OnClickTab(ETabType.Character));
        btn_battle.AddListener(() => OnClickTab(ETabType.Battle));
        btn_bag.AddListener(() => OnClickTab(ETabType.Bag));
        btn_goldlevel.AddListener(() => OnClickTab(ETabType.GoldLevel));

        shopRectTrans = btn_shop.GetComponent<RectTransform>();
        mainRectTrans = btn_main.GetComponent<RectTransform>();
        battleRectTrans = btn_battle.GetComponent<RectTransform>();
        bagRectTrans = btn_bag.GetComponent<RectTransform>();
        goldlevelRectTrans = btn_goldlevel.GetComponent<RectTransform>();

        obj_shopRedDot.SetActiveOptimize(false);
        obj_mainRedDot.SetActiveOptimize(false);
        obj_battleRedDot.SetActiveOptimize(false);
        obj_bagRedDot.SetActiveOptimize(false);
        obj_goldlevelRedDot.SetActiveOptimize(false);
        AddListener(EGameEvent.EquipRise, CheckEquipRedDot);
        AddListener(EGameEvent.UseHeroChange, CheckEquipRedDot);
        AddListener(EGameEvent.PropChange, OnPropChange);
        AddListener(EGameEvent.BattleClaimWaveReward, CheckLevelRedDot);
        AddListener(EGameEvent.FreeGetDiscountGoods, CheckFreeDiamond);
        AddListener(EGameEvent.LevelUpdate, RefreshLockMask);
        AddListener(EGameEvent.HeroViewed, CheckMainRedDot);
        AddListener(EGameEvent.TimesUpdate, CheckGoldLevelRedDot);
    }

    Color maskColor = new Color(0.2264151f, 0.2264151f, 0.2264151f);

    protected void RefreshLockMask(params object[] objs)
    {
        btn_shop_lockmask.SetActive(false);
        btn_battle_lockmask.SetActive(false);
        btn_main_lockmask.SetActive(!UnLockData.Instance.IsUnLock(201));
        btn_goldlevel_lockmask.SetActive(!UnLockData.Instance.IsUnLock(203));
        btn_bag_lockmask.SetActive(false);
        obj_shopLockMask.color = Color.white;
        obj_battleLockMask.color = Color.white;
        obj_bagLockMask.color = Color.white;
        obj_mainLockMask.color = UnLockData.Instance.IsUnLock(201) ? Color.white : maskColor;
        obj_goldlevelLockMask.color = UnLockData.Instance.IsUnLock(203) ? Color.white : maskColor;

        CheckGoldLevelRedDot();
    }

    protected override void OnOpen()
    {
        base.OnOpen();
        previousTab = ETabType.None;
        TabType = ETabType.Battle;

        AddListener(EGameEvent.NavigationGuide, CheckGuide);
        AddListener(EGameEvent.CheckGuide, CheckGuide);
        AddListener(EGameEvent.Jump, OnJump);

        SwitchTab();

        CheckMainRedDot();
        CheckEquipRedDot();
        CheckLevelRedDot();
        CheckGoldLevelRedDot();
        CheckFreeDiamond();
        RefreshLockMask();
        if (_initArgs.Length > 0)
        {
            var tab = (ETabType)_initArgs[0];
            OnClickTab(tab);
        }

        if (TabType == ETabType.Battle)
            CheckGuide();
    }

    private void SwitchTab(string args = "")
    {
        playAni = !HasGuide();
        OpenWindow(args);
        opend = false;
    }

    /// <summary>
    /// 设置页签显示状态
    /// </summary>
    private void SetSlectShow()
    {
        obj_shopSelected.gameObject.SetActiveOptimize(TabType == ETabType.Shop);
        obj_mainSelected.gameObject.SetActiveOptimize(TabType == ETabType.Character);
        obj_battleSelected.gameObject.SetActiveOptimize(TabType == ETabType.Battle);
        obj_goldlevelSelected.gameObject.SetActiveOptimize(TabType == ETabType.GoldLevel);
        obj_bagSelected.gameObject.SetActiveOptimize(TabType == ETabType.Bag);
    }

    /// <summary>
    /// 按钮动画
    /// </summary>
    /// <param name="tabType"></param>
    private void DoBtnanimation(ETabType tabType)
    {
        // if (previousTab == ETabType.None)
        // {
        //     SetSlectShow();
        //     return;
        // }

        // SetSlectShow();

        var curIndex = (int)tabType - 1;
        if (previousTab == ETabType.None)
        {
            for (int i = 0; i < selectList.Count; i++)
            {
                var img = selectList[i];
                if (curIndex != i)
                    img.color = Color.white;
            }
            selectList[curIndex].transform.localScale = Vector3.one * 1.05f;
            SetSlectShow();
            return;
        }
        // selectList[curIndex].DOFade(1, 0.3f);
        // selectList[(int)previousTab - 1]
        //     .DOFade(0, 0.5f)
        //     .OnComplete(() =>
        //     {
        SetSlectShow();
        // obj_shopSelected.gameObject.SetActiveOptimize(_tabType == ETabType.Shop);
        // obj_battleSelected.gameObject.SetActiveOptimize(_tabType == ETabType.Battle);
        // obj_bagSelected.gameObject.SetActiveOptimize(_tabType == ETabType.Bag);
        // });
        var btn = selectList[curIndex];
        var oldbtn = selectList[(int)previousTab - 1];
        btn.transform.DOScale(1.05f, 0.3f);
        var t = (RectTransform)btn_shop.transform.parent;
        oldbtn
            .transform.DOScale(1f, 0.3f)
            .OnUpdate(() =>
            {
                LayoutRebuilder.ForceRebuildLayoutImmediate(t);
            });
    }

    /// <summary>
    /// 是否有未完成的引导
    /// </summary>
    /// <returns></returns>
    private bool HasGuide()
    {
        bool value = false;
        switch (TabType)
        {
            case ETabType.Shop:
                break;
            case ETabType.Battle:
                if (GuideData.Instance.GuideStep == 204)
                {
                    value = true;
                }
                break;
            case ETabType.Bag:
                if (
                    GuideData.Instance.GuideStep == 201
                    || GuideData.Instance.GuideStep == 401
                    || GuideData.Instance.GuideStep == 402
                    || GuideData.Instance.GuideStep == 404
                    || GuideData.Instance.GuideStep == 202
                )
                {
                    value = true;
                }
                break;
            default:
                break;
        }
        return value;
    }

    Dictionary<ETabType, int> unLockId = new()
    {
        { ETabType.Character, 201 },
        { ETabType.GoldLevel, 203 },
    };

    private void OnClickTab(ETabType tabType)
    {
        if (unLockId.TryGetValue(tabType, out int id) && !UnLockData.Instance.IsUnLock(id))
        {
            Util.FloatTips(UnLockData.Instance.GetUnLockDes(id));
            return;
        }
        playAni = !HasGuide();
        switch (tabType) // 有引导暂时不放动画
        {
            case ETabType.Shop:
                break;
            case ETabType.Character:
                break;
            case ETabType.Battle:
                if (GuideData.Instance.GuideStep == 204)
                {
                    GuideData.NextStep();
                }
                break;
            case ETabType.GoldLevel:
                break;
            case ETabType.Bag:
                if (GuideData.Instance.GuideStep == 201 || GuideData.Instance.GuideStep == 401)
                {
                    GuideData.NextStep();
                }
                break;
            default:
                break;
        }

        AudioManager.ClickSound();
        if (TabType == tabType)
            return;
        // 关闭当前的页签
        // CloseWindow(_tabType);
        previousTab = TabType;
        TabType = tabType;
        SwitchTab();
    }

    private void OpenWindow(string args = "")
    {
        switch (TabType)
        {
            case ETabType.Shop:
                // 跳到指定的商店页签
                var shopType = string.IsNullOrEmpty(args)
                    ? EShopType.DiscountShop
                    : Enum.Parse<EShopType>(args);
                UIManager.Instance.OpenWindow2(
                    EWindowName.Shop,
                    EWindowLayer.Normal,
                    -1,
                    WindowCall,
                    shopType
                );
                break;
            case ETabType.Character:
                if (!string.IsNullOrEmpty(args))
                {
                    UIManager.Instance.OpenWindow2(
                        EWindowName.RaisingCharacter,
                        EWindowLayer.Normal,
                        -1,
                        WindowCall,
                        int.Parse(args)
                    );
                }
                else
                    UIManager.Instance.OpenWindow2(
                        EWindowName.RaisingCharacter,
                        EWindowLayer.Normal,
                        -1,
                        WindowCall
                    );
                break;
            case ETabType.Battle:
                // 预留接口,可以跳到指定的关卡界面
                UIManager.Instance.OpenWindow2(
                    EWindowName.Level,
                    EWindowLayer.Normal,
                    -1,
                    WindowCall,
                    args
                );
                break;
            case ETabType.GoldLevel:
                // 跳到指定的商店页签
                UIManager.Instance.OpenWindow2(
                    EWindowName.GoldLevelWindow,
                    EWindowLayer.Normal,
                    -1,
                    WindowCall
                );
                break;
            case ETabType.Bag:
                UIManager.Instance.OpenWindow2(
                    EWindowName.Equip,
                    EWindowLayer.Normal,
                    -1,
                    WindowCall
                );
                break;
        }
    }

    private void WindowCall(WindowBase window)
    {
        if (opend)
        {
            return;
        }
        // DOTween.Complete(); // 用mask遮住了
        string winName = EWindowName.Shop;
        if (!playAni)
        {
            // 不放动画
            SetSlectShow();
            UIManager.Instance.CloseWindow(winName);
            return;
        }
        DoBtnanimation(TabType);

        switch (previousTab)
        {
            case ETabType.Shop:
                winName = EWindowName.Shop;
                break;
            case ETabType.Character:
                winName = EWindowName.RaisingCharacter;
                break;
            case ETabType.Battle:
                winName = EWindowName.Level;
                break;
            case ETabType.GoldLevel:
                winName = EWindowName.GoldLevelWindow;
                break;
            case ETabType.Bag:
                winName = EWindowName.Equip;
                break;
        }
        eAniDiraction dir = eAniDiraction.right;
        if (previousTab > TabType) // 打开了左边的界面
        {
            dir = eAniDiraction.left;
        }
        OpenAnimation(window.transform, dir);
        CloseAnimation(
            UIManager.Instance.GetOpenedWindow(winName)?.transform,
            dir,
            () =>
            {
                UIManager.Instance.CloseWindow(winName);
            }
        );
    }

    Vector3 aniPos = new(Screen.width, 0, 0);

    /// <summary>
    /// 关闭动画
    /// </summary>
    public void CloseAnimation(Transform taget, eAniDiraction diraction, Action call = null)
    {
        if (taget == null)
            return;
        var bPos = 720;
        if (diraction == eAniDiraction.right)
        {
            bPos = -720;
        }
        var rect = (RectTransform)taget;
        rect.DOLocalMoveX(bPos, 0.35f)
            .SetEase(Ease.Linear)
            .OnComplete(() =>
            {
                call?.Invoke();
            })
            .From(Vector3.zero);
    }

    /// <summary>
    /// 开启动画
    /// </summary>
    public void OpenAnimation(Transform taget, eAniDiraction diraction, Action call = null)
    {
        if (taget == null)
            return;
        var rect = (RectTransform)taget;
        if (diraction == eAniDiraction.right)
        {
            aniPos = new(720, 0, 0);
        }
        else
        {
            aniPos = new Vector3(-720, 0, 0);
        }

        lockMask.enabled = true;

        rect.DOLocalMoveX(0, 0.35f)
            .SetEase(Ease.Linear)
            .OnComplete(() =>
            {
                call?.Invoke();
                lockMask.enabled = false;
            })
            .From(aniPos);
    }

    private void CloseWindow(ETabType tabType)
    {
        switch (tabType)
        {
            case ETabType.Shop:
                UIManager.Instance.CloseWindow(EWindowName.Shop);
                break;
            case ETabType.Battle:
                UIManager.Instance.CloseWindow(EWindowName.Level);
                break;
            case ETabType.Bag:
                UIManager.Instance.CloseWindow(EWindowName.Equip);
                break;
        }
    }

    private void OnJump(params object[] args)
    {
        if (args.Length < 2)
            return;
        var tab = (ETabType)args[0];
        var arg = (string)args[1];
        if (Enum.TryParse<EShopType>(arg, out var shopType))
        {
            opend = UIManager.Instance.GetOpenedWindow(EWindowName.Shop);
        }
        else if (tabWindowDic.TryGetValue(tab, out string windowName))
            opend = UIManager.Instance.GetOpenedWindow(windowName);
        previousTab = TabType;
        TabType = tab;
        SwitchTab(arg);
    }

    /// <summary>
    /// 尝试开始引导
    /// </summary>
    private void TryStartGuide(Action<WindowBase> callBack)
    {
        var complete = GuideData.IsGuideComplete(4);
        if (!complete) // 还没有没完成引导
        {
            if (GuideData.Instance.GuideStep == 0) // 是否有正在进行中的引导
                if (EquipData.Instance.CheckHaveEquipCanUp()) // 满足触发条件
                {
                    GuideData.StartGuide(4);
                    UIManager.Instance.OpenWindow2(
                        EWindowName.Guide,
                        EWindowLayer.Guide,
                        call: callBack
                    );
                    return;
                }
        }
        callBack?.Invoke(null);
    }

    private void CheckGuide(params object[] args)
    {
        TryStartGuide(
            (win) =>
            {
                int guideStep = GuideData.Instance.GuideStep;
                if (guideStep > 0)
                {
                    var cfg = GuideConfig.Instance.Get(guideStep);
                    if (guideStep == 201 || guideStep == 401)
                    {
                        Vector3[] corners = new Vector3[4];
                        bagRectTrans.GetWorldCorners(corners);
                        var pos = new Vector3(
                            (corners[0].x + corners[2].x) * 0.5f,
                            (corners[0].y + corners[2].y) * 0.5f,
                            corners[0].z
                        );

                        var guideArg = new GuideArg();
                        guideArg.Type = EGuideType.ClickAndTalk;
                        guideArg.Rect = new Rect(pos, bagRectTrans.sizeDelta);
                        guideArg.ExtraSize = new Vector2(20, 15);
                        guideArg.TalkContent = GetLocalize(cfg.Talk);
                        guideArg.TalkPos = pos;
                        GlobalEvent.DispatchEvent(EGameEvent.GuideStep, guideArg);
                    }
                    else if (guideStep == 204)
                    {
                        Vector3[] corners = new Vector3[4];
                        battleRectTrans.GetWorldCorners(corners);
                        var pos = new Vector3(
                            (corners[0].x + corners[2].x) * 0.5f,
                            (corners[0].y + corners[2].y) * 0.5f,
                            corners[0].z
                        );

                        var guideArg = new GuideArg();
                        guideArg.Type = EGuideType.Click;
                        guideArg.Rect = new Rect(pos, battleRectTrans.sizeDelta);
                        guideArg.ExtraSize = new Vector2(20, 15);
                        guideArg.TalkPos = pos;
                        GlobalEvent.DispatchEvent(EGameEvent.GuideStep, guideArg);
                    }
                }
            }
        );
    }
    
    private void CheckMainRedDot(params object[] objs)
    {
        obj_mainRedDot.SetActiveOptimize(UnLockData.Instance.IsUnLock(201) && HasNewHero());
    }

    private bool HasNewHero()
    {
        var list = HeroConfig.Instance.GetList();
        for (int i = 0; i < list.Count; i++)
        {
            var hero = list[i];
            if (i > 0 && !HeroData.Instance.IsViewed(hero.ID))
                return true;
        }

        return false;
    }

    private void CheckEquipRedDot(params object[] objs)
    {
        obj_bagRedDot.SetActiveOptimize(EquipData.Instance.CheckHaveEquipCanUp());
    }

    private void OnPropChange(params object[] objs)
    {
        CheckEquipRedDot();
    }

    private void CheckLevelRedDot(params object[] objs)
    {
        obj_battleRedDot.SetActiveOptimize(LevelData.Instance.CheckHaveReward());
    }

    private void CheckFreeDiamond(params object[] objs)
    {
        obj_shopRedDot.SetActiveOptimize(ShopData.Instance.IsFreeDiamond);
    }

    private void CheckGoldLevelRedDot(params object[] args)
    {
        obj_goldlevelRedDot.SetActiveOptimize(
            UnLockData.Instance.IsUnLock(203) &&
            (TimesData.GetTimes(40001) > 0 || TimesData.GetTimes(40003) > 0));
    }
}
