using System.Collections;
using System.Threading;
using Cysharp.Threading.Tasks;
using UnityEngine;
using UnityEngine.UI;

public static class ScrollRectExtensions
{
    // ScrollRect滚动到指定位置
    public static async UniTask ScrollToItem(this ScrollRect scrollRect, int itemIndex, float duration = 0f,
        CancellationToken cancellationToken = default, int childCount = -1)
    {
        // Debug.LogFormat("[{0}] {1} ScrollToIndex: {2}", Time.frameCount, scrollRect.name, itemIndex);

        // 等待列表准备好。
        scrollRect.StopMovement();
        await UniTask.Yield(cancellationToken); // 等待一次物理更新，确保列表已准备好。
        if (childCount == -1)
            childCount = scrollRect.content.childCount;
        float normalizedPosition = itemIndex / (float)(childCount - 1);
        Vector2 targetPosition = new Vector2(scrollRect.horizontalNormalizedPosition, scrollRect.verticalNormalizedPosition);

        if (scrollRect.horizontal)
        {
            targetPosition.x = CalculateHorizontalPosition(scrollRect, childCount, normalizedPosition);
        }
        else if (scrollRect.vertical)
        {
            targetPosition.y = 1 - CalculateVerticalPosition(scrollRect, childCount, normalizedPosition);
        }

        Vector2 startPosition = new Vector2(scrollRect.horizontalNormalizedPosition, scrollRect.verticalNormalizedPosition);
        float elapsedTime = 0f;

        while (elapsedTime < duration)
        {
            float normalizedTime = elapsedTime / duration;
            scrollRect.normalizedPosition = Vector2.Lerp(startPosition, targetPosition, normalizedTime);
            elapsedTime += Time.deltaTime;
            await UniTask.Yield(cancellationToken);
        }

        scrollRect.normalizedPosition = targetPosition;

        if (scrollRect.horizontal)
            scrollRect.GetComponentInChildren<HorizontalLayoutGroup>().CalculateLayoutInputHorizontal();
        if (scrollRect.vertical)
            scrollRect.GetComponentInChildren<VerticalLayoutGroup>().CalculateLayoutInputVertical();
    }

    private static float CalculateHorizontalPosition(ScrollRect scrollRect, int childCount, float normalizedPosition)
    {
        float viewportWidth = scrollRect.viewport.rect.width;
        float contentWidth = scrollRect.content.sizeDelta.x;
        float paddingWidth = scrollRect.padding().left + scrollRect.padding().right;
        float spacingWidth = (childCount - 1) * scrollRect.spacing();
        float totalWidth = contentWidth + paddingWidth + spacingWidth;
        float scrollWidth = totalWidth - viewportWidth;
        float scrollPosition = normalizedPosition * scrollWidth;
        float normalizedScrollPosition = scrollPosition / scrollWidth;
        return normalizedScrollPosition;
    }

    private static float CalculateVerticalPosition(ScrollRect scrollRect, int childCount, float normalizedPosition)
    {
        float viewportHeight = scrollRect.viewport.rect.height;
        float contentHeight = scrollRect.content.sizeDelta.y;
        float paddingHeight = scrollRect.padding().top + scrollRect.padding().bottom;
        float spacingHeight = (childCount - 1) * scrollRect.spacing();
        float totalHeight = contentHeight + paddingHeight + spacingHeight;
        // Debug.Log("contentHeight: " + contentHeight + " paddingHeight: " + paddingHeight + " spacingHeight: " + spacingHeight + " totalHeight: " + totalHeight + " viewportHeight: " + viewportHeight);
        float scrollHeight = totalHeight - viewportHeight;
        float scrollPosition = normalizedPosition * scrollHeight;
        float normalizedScrollPosition = scrollPosition / scrollHeight;
        return normalizedScrollPosition;
    }

    // 根据水平或垂直组件获取spacing
    public static float spacing(this ScrollRect scrollRect)
    {
        if (scrollRect.horizontal)
        {
            return scrollRect.content.GetComponent<HorizontalLayoutGroup>().spacing; // 水平布局的spacing是水平方向的间距。
        }
        else if (scrollRect.vertical)
        {
            return scrollRect.content.GetComponent<VerticalLayoutGroup>().spacing; // 垂直布局的spacing是垂直方向的间距。
        }
        return 0f;
    }

    // 根据水平或垂直组件获取padding
    public static RectOffset padding(this ScrollRect scrollRect)
    {
        if (scrollRect.horizontal)
        {
            return scrollRect.content.GetComponent<HorizontalLayoutGroup>().padding; // 水平布局的padding是水平方向的间距。
        }
        else if (scrollRect.vertical)
        {
            return scrollRect.content.GetComponent<VerticalLayoutGroup>().padding; // 垂直布局的padding是垂直方向的间距。
        }
        return new RectOffset(0, 0, 0, 0); // 如果没有水平或垂直组件，则返回默认的RectOffset。
    }
}


