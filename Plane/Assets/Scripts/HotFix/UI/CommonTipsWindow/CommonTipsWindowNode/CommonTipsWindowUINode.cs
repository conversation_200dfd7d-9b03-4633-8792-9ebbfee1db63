using TMPro;
 using UnityEngine;
 using UnityEngine.UI;
 using GameCommon;
public   class CommonTipsWindowUINode 
{
public TMPro.TextMeshProUGUI txt_Title;
public UnityEngine.UI.Button btn_Close;
public UnityEngine.Transform tf_AdGetEuip;
public UnityEngine.UI.Image img_equip;
public TMPro.TextMeshProUGUI txt_AdContent;
public UnityEngine.UI.Button btn_AdEquip;
public TMPro.TextMeshProUGUI txt_GetEquipBtn;
public TMPro.TextMeshProUGUI txt_GetEquipTimes;
public UnityEngine.Transform tf_GetItem;
public UnityEngine.UI.Image img_item;
public TMPro.TextMeshProUGUI txt_itemCount;
public TMPro.TextMeshProUGUI txt_ItemContent;
public UnityEngine.UI.Button btn_GetItem;
public TMPro.TextMeshProUGUI txt_GetItemBtn;
public TMPro.TextMeshProUGUI txt_GetItemTimes;
public UnityEngine.Transform tf_text;
public TMPro.TextMeshProUGUI txt_Content;
public UnityEngine.UI.Button btn_Confirm;
public TMPro.TextMeshProUGUI txt_Confirm;
 public  void FindComponent(GameObject obj){
txt_Title=obj.transform.Find("CommonTipBg/bg/frameBg/txt_Title").GetComponent<TMPro.TextMeshProUGUI>();
btn_Close=obj.transform.Find("CommonTipBg/bg/frameBg/btn_Close").GetComponent<UnityEngine.UI.Button>();
tf_AdGetEuip=obj.transform.Find("CommonTipBg/bg/frameBg/tf_AdGetEuip").GetComponent<UnityEngine.Transform>();
img_equip=obj.transform.Find("CommonTipBg/bg/frameBg/tf_AdGetEuip/img_equip").GetComponent<UnityEngine.UI.Image>();
txt_AdContent=obj.transform.Find("CommonTipBg/bg/frameBg/tf_AdGetEuip/txt_AdContent").GetComponent<TMPro.TextMeshProUGUI>();
btn_AdEquip=obj.transform.Find("CommonTipBg/bg/frameBg/tf_AdGetEuip/btn_AdEquip").GetComponent<UnityEngine.UI.Button>();
txt_GetEquipBtn=obj.transform.Find("CommonTipBg/bg/frameBg/tf_AdGetEuip/btn_AdEquip/txt_GetEquipBtn").GetComponent<TMPro.TextMeshProUGUI>();
txt_GetEquipTimes=obj.transform.Find("CommonTipBg/bg/frameBg/tf_AdGetEuip/btn_AdEquip/txt_GetEquipTimes").GetComponent<TMPro.TextMeshProUGUI>();
tf_GetItem=obj.transform.Find("CommonTipBg/bg/frameBg/tf_GetItem").GetComponent<UnityEngine.Transform>();
img_item=obj.transform.Find("CommonTipBg/bg/frameBg/tf_GetItem/img_item").GetComponent<UnityEngine.UI.Image>();
txt_itemCount=obj.transform.Find("CommonTipBg/bg/frameBg/tf_GetItem/txt_itemCount").GetComponent<TMPro.TextMeshProUGUI>();
txt_ItemContent=obj.transform.Find("CommonTipBg/bg/frameBg/tf_GetItem/txt_ItemContent").GetComponent<TMPro.TextMeshProUGUI>();
btn_GetItem=obj.transform.Find("CommonTipBg/bg/frameBg/tf_GetItem/btn_GetItem").GetComponent<UnityEngine.UI.Button>();
txt_GetItemBtn=obj.transform.Find("CommonTipBg/bg/frameBg/tf_GetItem/btn_GetItem/txt_GetItemBtn").GetComponent<TMPro.TextMeshProUGUI>();
txt_GetItemTimes=obj.transform.Find("CommonTipBg/bg/frameBg/tf_GetItem/btn_GetItem/txt_GetItemTimes").GetComponent<TMPro.TextMeshProUGUI>();
tf_text=obj.transform.Find("CommonTipBg/bg/frameBg/tf_text").GetComponent<UnityEngine.Transform>();
txt_Content=obj.transform.Find("CommonTipBg/bg/frameBg/tf_text/txt_Content").GetComponent<TMPro.TextMeshProUGUI>();
btn_Confirm=obj.transform.Find("CommonTipBg/bg/frameBg/tf_text/btn_Confirm").GetComponent<UnityEngine.UI.Button>();
txt_Confirm=obj.transform.Find("CommonTipBg/bg/frameBg/tf_text/btn_Confirm/txt_Confirm").GetComponent<TMPro.TextMeshProUGUI>();
}
}
