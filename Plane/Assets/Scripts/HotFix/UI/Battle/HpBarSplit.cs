using System.Collections.Generic;
using UnityEngine;
using Object = UnityEngine.Object;

public class HpBarSplit : MonoBehaviour
{
    [SerializeField] private RectTransform bar;
    [SerializeField] private RectTransform shieldBar;
    [SerializeField] private RectTransform hpBar;
    [SerializeField] private RectTransform splitter;
    [SerializeField] private float padding;

    // [SerializeField]
    private float _maxHp;
    // [SerializeField]
    private float _hp;
    // [SerializeField]
    private float _shield;
    // [SerializeField]
    private float _perGrid;

    private float _maxWidth;
    private readonly List<RectTransform> _splitters = new();

    private void Awake()
    {
        _maxWidth = bar.sizeDelta.x - padding * 2;
        _splitters.Add(splitter);
    }

    public void Refresh(float hp, float maxHp, float shield, float perGrid)
    {
        _hp = Mathf.Max(hp, 0);
        _maxHp = maxHp;
        _shield = Mathf.Max(shield, 0);
        _perGrid = perGrid;

        Refresh();
    }

    [ContextMenu("Refresh")]
    private void Refresh()
    {
        float total = _maxHp + _shield;
        shieldBar.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal,
            (_hp + _shield) / total * _maxWidth + padding * 2);
        hpBar.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal,
            _hp / total * _maxWidth + padding * 2);

        float gridCount = total / _perGrid;
        float gridWidth = _maxWidth / gridCount;
        int splitterCount = Mathf.Max(Mathf.CeilToInt(gridCount) - 1, 0);
        for (int i = 0; i < Mathf.Max(splitterCount, _splitters.Count); i++)
        {
            RectTransform s;
            if (i < _splitters.Count)
                s = _splitters[i];
            else
            {
                s = Instantiate(splitter, splitter.parent);
                _splitters.Add(s);
            }

            s.gameObject.SetActive(i < splitterCount);
            var pos = s.anchoredPosition;
            pos.x = gridWidth * (i + 1) + padding;
            s.anchoredPosition = pos;
        }
    }
}