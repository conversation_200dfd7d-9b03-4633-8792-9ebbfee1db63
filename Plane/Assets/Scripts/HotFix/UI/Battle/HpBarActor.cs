using Battle;
using GameCommon;
using TMPro;
using UnityEngine;

public class HpBarActor : MonoBehaviour
{
    [SerializeField] private HpBarSplit bar;
    [SerializeField] private TextMeshProUGUI text;
    [SerializeField] private RectTransform line;

    public ActorBase Actor { private get; set; }

    private int _hpCache;
    private int _maxHpCache;
    private int _shieldCache;
    private Vector3 _vel;

    private void LateUpdate()
    {
        if (!Actor)
            return;

        if (_hpCache != Mathf.CeilToInt((float)Actor.Hp) ||
            _maxHpCache != Mathf.CeilToInt((float)Actor.MaxHp) ||
            _shieldCache != Mathf.CeilToInt((float)Actor.Shield))
        {
            _hpCache = Mathf.CeilToInt((float)Actor.Hp);
            _maxHpCache = Mathf.CeilToInt((float)Actor.MaxHp);
            _shieldCache = Mathf.CeilToInt((float)Actor.Shield);

            text.text = _shieldCache > 0
                ? $"<#27FF0D>{Mathf.Max(_hpCache, 0)}<#FFFFFF>({_shieldCache})"
                : $"<#27FF0D>{Mathf.Max(_hpCache, 0)}";

            float perGrid = ConstValue.HpBarPerGrid.Length > 0 ? ConstValue.HpBarPerGrid[^1].PerGrid : 100;
            foreach (var (hp, value) in ConstValue.HpBarPerGrid)
            {
                if (Actor.MaxHp + Actor.Shield <= hp)
                {
                    perGrid = value;
                    break;
                }
            }
            bar.Refresh((float)Actor.Hp, (float)Actor.MaxHp, (float)Actor.Shield, perGrid);
        }

        var screenPos = BattleRoot.Instance.Camera.WorldToScreenPoint(Actor.HpPos);
        RectTransformUtility.ScreenPointToLocalPointInRectangle((RectTransform)transform.parent,
            screenPos, UIManager.Instance.UICamera, out var pos);

        transform.localPosition = Vector3.SmoothDamp(transform.localPosition, pos, ref _vel, 0.1f);

        RectTransformUtility.ScreenPointToLocalPointInRectangle((RectTransform)line.parent,
            screenPos, UIManager.Instance.UICamera, out var lineTargetPos);
        Vector3 vector = lineTargetPos - line.anchoredPosition;
        line.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, vector.magnitude);
        line.localRotation = Quaternion.AngleAxis(90, Vector3.forward) *
                             Quaternion.LookRotation(Vector3.forward, vector);
    }
}