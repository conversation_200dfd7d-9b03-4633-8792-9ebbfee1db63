using System;
using System.Collections;
using System.Collections.Generic;
using Battle;
using DG.Tweening;
using GameCommon;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
#if GRAVITY_WECHAT_GAME_MODE
using WeChatWASM;
#endif

public class BattleWindow : WindowBase
{
    public Transform HpAndShield;
    public BattleCircleView CircleView;
    public BattleFloatView floatView;
    public BattleFloatView criFloatView;
    public HpBarView hpBarView;
    public HpView hpView;
    public BuffTipsView buffTipsView;
    public BossBarView bossBarView;
    public HpBarActor hpBarHero;
    public Button btnPause;
    public TextMeshProUGUI txtWave;
    public TextMeshProUGUI txtTime;
    public TextMeshProUGUI txt_coinYin;
    public TextMeshProUGUI txt_hp;
    public TextMeshProUGUI txtGoldLevelTime;
    public Slider slider_hp;
    public TextMeshProUGUI txt_shield;
    public Slider slider_shield;
    public Image imgHurt;
    public Image drop_coin;
    public Image drop_item;
    public Transform coin;
    public Transform leveldrop;
    public Transform coinleveldrop;
    public TextMeshProUGUI txt_dropcoin;
    public TextMeshProUGUI txt_dropitem;
    public TextMeshProUGUI txt_coinleveldropcoin;
    public BattleArtFontView berserkArtFont;
    public BuffStateView buffStateTemp;
    public Transform GoldLevelEquipList;
    public GoldLevelEquipItem GoldLevelItem;
    private List<GoldLevelEquipItem> _goldLevelEquipItemList = new List<GoldLevelEquipItem>();

    private List<BattleFloatView> _listFloat = new List<BattleFloatView>();
    private List<BattleFloatView> _listCriFloat = new List<BattleFloatView>();
    private List<HpBarView> _listHpBar = new List<HpBarView>();
    private List<HpView> _listHpView = new List<HpView>();
    private List<BattleSkillItem> _listSkillItems = new List<BattleSkillItem>();
    private List<BuffStateView> _listBuffStateItems = new List<BuffStateView>();
    public Dictionary<int, int> BuffSkillIdNumMap = new Dictionary<int, int>();
    public Transform GoldLevelTransform;
    public TextMeshProUGUI txt_GoldLevelCount;
    private Coroutine _corHurtFade;
    private Sequence _sequence;
    private Sequence _sequence2;

    private Sequence _sequence3;
    private Sequence _sequence4;
    private int battleType;

    public Transform GoldLevelTargetTransform;


    protected override void OnInit()
    {
        base.OnInit();
        btnPause.AddListener(OnClickPause);
        _listBuffStateItems.Add(buffStateTemp);
        buffStateTemp.Hide();

        _totalOrder = 10;
    }

    private void OnClickPause()
    {
        if (BattleManager.Instance.IsInSettle)
        {
            return;
        }
        AudioManager.ClickSound();
        UIManager.Instance.OpenPop(EWindowName.BuffListWindow, new object[] { 0, battleType });
    }


    protected override void OnOpen()
    {
        base.OnOpen();
        battleType = _initArgs != null && _initArgs.Length > 0 ? (int)_initArgs[0] : 3;
        RefreshMoney();
        var hero = BattleManager.Instance.Hero;
        if (hero != null)
            RefreshShield(heroShield, hero.ShieldPercent);
        hpBarHero.Actor = hero;
        hpBarHero.gameObject.SetActive(false);

        bossBarView.Hide();
        imgHurt.transform.localScale = Vector3.zero;
        txt_dropcoin.text = BattleManager.Instance.ExtraCoin.ToShort();
        txt_dropitem.text = BattleManager.Instance.ExtraItem.ToShort();
        if (BattleManager.LevelModel)
        {
            leveldrop.gameObject.SetActive(true);
            GoldLevelTransform.gameObject.SetActive(false);
            HpAndShield.gameObject.SetActive(true);
            txtWave.gameObject.SetActive(true);
        }
        else
        {
            leveldrop.gameObject.SetActive(false);
            GoldLevelTransform.gameObject.SetActive(true);
            HpAndShield.gameObject.SetActive(false);
            txtWave.gameObject.SetActive(false);
            txt_GoldLevelCount.text = GoldLevelManager.Instance.GetTargetCount();
            txt_coinleveldropcoin.text = "0";
            BuffSkillIdNumMap = new Dictionary<int, int>();

            GoldLevelTargetTransform.localScale = Vector3.one * 2;
            GoldLevelTargetTransform.localPosition = new Vector3(0, -800, 0);
            GoldLevelTargetTransform.DOLocalMove(Vector3.zero, 1f);
            GoldLevelTargetTransform.DOScale(1, 1f);
        }

        coin.gameObject.SetActive(BattleManager.LevelModel);

        if (BattleManager.LevelModel)
        {
            AddListener(EGameEvent.BattleFloat, OnFloat);
        }
        AddListener(EGameEvent.BattleArtFont, OnArtFont);
        AddListener(EGameEvent.BattleHpBar, OnCreateHpBar);
        AddListener(EGameEvent.PropChange, RefreshMoney);
        AddListener(EGameEvent.BoxCoinChange, RefreshMoney);
        AddListener(EGameEvent.BattleHurt, OnBattleHurt);
        AddListener(EGameEvent.BattleAddFlyBuff, OnAddFlyBuff);
        AddListener(EGameEvent.ExtraDropRewardCoin, OnExtraDropRewardCoin);
        AddListener(EGameEvent.ExtraDropRewardChip, OnExtraDropRewardChip);
        AddListener(EGameEvent.GoldLevelBuff, OnGetBuff);
        AddListener(EGameEvent.GoldLevelKillMonsterCount, OnGoldLevelKillMonsterCount);
        AddListener(EGameEvent.BattleStart, OnBattleStart);
        AddListener(EGameEvent.BattleSettle, OnBattleSettle);

        AudioManager.PlayMusic(EAudio.MusicBattle);

        if (BattleManager.LevelModel)
        {
            txtWave.text = BattleManager.Instance.GetShowWave();
        }
        else
        {
            txtGoldLevelTime.text = GoldLevelManager.Instance.BattleTime.ToShort();
        }

        InitGoldLevelSkill();
        ResetTime();
    }

    private void OnBattleStart(object[] args)
    {
        if (BattleManager.Instance.Hero != null)
        {
            hpBarHero.gameObject.SetActive(true);
        }
    }

    private void OnBattleSettle(object[] args)
    {
        hpBarHero.gameObject.SetActive(false);
    }

    private void ResetTime()
    {
#if UNITY_EDITOR
        txtTime.gameObject.SetActive(true);
#else
        txtTime.gameObject.SetActive(false);
#endif
    }

    protected override void OnClose()
    {
        base.OnClose();

        for (int i = _listFloat.Count - 1; i >= 0; i--)
        {
            _listFloat[i].Hide();
        }

        for (int i = _listCriFloat.Count - 1; i >= 0; i--)
        {
            _listCriFloat[i].Hide();
        }

        for (int i = _listHpBar.Count - 1; i >= 0; i--)
        {
            _listHpBar[i].Hide();
        }

        for (int i = _listHpView.Count - 1; i >= 0; i--)
        {
            _listHpView[i].Hide();
        }

        for (int i = _listBuffStateItems.Count - 1; i >= 0; i--)
        {
            _listBuffStateItems[i].Hide();
        }
    }

    private void RefreshMoney(params object[] args)
    {
        txt_coinYin.text = BattleData.Instance.Coin.ToShort();
        //txt_coinJin.text = BagData.Instance.GetPropNum((int)ECoinType.Gold).ToShort();
    }

#if GRAVITY_WECHAT_GAME_MODE
    private VibrateShortOption _vibrateShortOption = new VibrateShortOption() { type = "heavy" };
#endif

    private void OnBattleHurt(params object[] args)
    {
        if (_corHurtFade == null)
        {
            _corHurtFade = StartCoroutine(DoHurtFade());
        }

        BattleRoot.Instance.Shake(1);
        if (SettingData.Instance.GetShakeValue > 0)
        {
#if GRAVITY_WECHAT_GAME_MODE
            WX.VibrateShort(_vibrateShortOption);
#else
            Handheld.Vibrate();
#endif
        }
    }

    private IEnumerator DoHurtFade()
    {
        imgHurt.transform.localScale = Vector3.one;
        imgHurt.CrossFadeAlpha(1, 0.3f, false);
        yield return new WaitForSeconds(0.3f);
        imgHurt.CrossFadeAlpha(0, 0.3f, false);
        yield return new WaitForSeconds(0.3f);
        imgHurt.transform.localScale = Vector3.zero;
        _corHurtFade = null;
    }

    private void OnAddFlyBuff(params object[] args)
    {
        var buff = (BuffActionBase)args[0];
        var cfg = BuffActionConfig.Instance.Get(buff.BuffID);
        if (cfg == null || string.IsNullOrEmpty(cfg.Icon))
        {
            return;
        }

        BuffStateView stateView = null;
        for (int i = 0; i < _listBuffStateItems.Count; i++)
        {
            if (!_listBuffStateItems[i].InShow)
            {
                stateView = _listBuffStateItems[i];
                break;
            }
        }

        if (stateView == null)
        {
            stateView = Util.Clone(buffStateTemp, $"item{_listBuffStateItems.Count}");
            _listBuffStateItems.Add(stateView);
        }
        stateView.Show(buff);
    }

    private void OnStart(params object[] args)
    {


        //togSkill.isOn = _isShowSkill;
    }

    private void Update()
    {
        UpdateHpAndShield();
        UpdateTime();
    }

    private void OnArtFont(params object[] args)
    {
        var fontType = (EArtFont)args[0];
        var screenPos = (Vector3)args[1];
        BattleArtFontView artFont = null;
        switch (fontType)
        {
            case EArtFont.Berserk:
            case EArtFont.Avatar:
            case EArtFont.Nuclear:
            case EArtFont.Laser:
            case EArtFont.Invincible:
            case EArtFont.SkillAttr:
                artFont = berserkArtFont;
                break;
            default:
                break;
        }
        RectTransformUtility.ScreenPointToLocalPointInRectangle((RectTransform)artFont.transform.parent,
            screenPos, UIManager.Instance.UICamera, out var pos);
        BattleArtFontView artFontView = Util.Clone(artFont, "baozou");
        artFontView.Show(pos, fontType);
    }

    private void OnFloat(params object[] args)
    {
        var content = (string)args[0];
        var damageType = (EDamageType)args[1];
        var screenPos = (Vector3)args[2];

        var rectTrans = damageType == EDamageType.Cri
            ? (RectTransform)criFloatView.transform.parent
            : (RectTransform)floatView.transform.parent;

        RectTransformUtility.ScreenPointToLocalPointInRectangle(rectTrans, screenPos, UIManager.Instance.UICamera,
            out var pos);

        if (damageType == EDamageType.Normal)
        {
            var showCount = 0;
            var areaCount = 0;
            for (int i = 0; i < _listFloat.Count; i++)
            {
                if (!_listFloat[i].IsEnd)
                {
                    showCount++;
                    if (Vector2.Distance(_listFloat[i].transform.localPosition, pos) < 30f)
                    {
                        areaCount++;
                    }
                }
            }

            //最低显示数量
            if (showCount > 50 || areaCount > 3)
            {
                return;
            }
        }

        BattleFloatView view = damageType == EDamageType.Cri ? GetCriFloat() : GetFloat();
        view.Show(content, damageType, pos);
    }


    private BattleFloatView GetFloat()
    {
        BattleFloatView floatNum = null;
        for (int i = 0; i < _listFloat.Count; i++)
        {
            if (_listFloat[i].IsEnd)
            {
                floatNum = _listFloat[i];
                break;
            }
        }

        if (null == floatNum)
        {
            floatNum = Util.Clone(floatView, $"float_{_listFloat.Count}");
            floatNum.gameObject.SetActiveOptimize(true);
            _listFloat.Add(floatNum);
        }

        return floatNum;
    }

    private BattleFloatView GetCriFloat()
    {
        BattleFloatView floatNum = null;
        for (int i = 0; i < _listCriFloat.Count; i++)
        {
            if (_listCriFloat[i].IsEnd)
            {
                floatNum = _listCriFloat[i];
                break;
            }
        }

        if (null == floatNum)
        {
            floatNum = Util.Clone(criFloatView, $"criFloat_{_listCriFloat.Count}");
            floatNum.gameObject.SetActiveOptimize(true);
            _listCriFloat.Add(floatNum);
        }

        return floatNum;
    }


    private void OnCreateHpBar(params object[] args)
    {
        if (BattleManager.LevelModel)
        {
            var insId = (int)args[0];
            var actorType = (EActorType)args[1];

            if (actorType == EActorType.Enemy)
            {
                var actor = BattleActorManager.Instance.GetEnemy(insId);
                if (actor != null)
                {
                    if (actor.EnemyType == EEnemyType.Boss)//boss
                    {
                        bossBarView.Show(actor);
                    }
                    else//精英
                    {
                        GetHpBar().Show(actor);
                    }
                }
            }
        }
        else
        {
            var insId = (int)args[0];
            var actorType = (EActorType)args[1];

            if (actorType == EActorType.Enemy)
            {
                var actor = BattleActorManager.Instance.GetEnemy(insId);
                if (actor != null)
                {
                    GetHpView().Show(actor);
                }
            }
        }

    }

    private HpBarView GetHpBar()
    {
        HpBarView barView = null;
        for (var i = 0; i < _listHpBar.Count; i++)
        {
            if (!_listHpBar[i].gameObject.activeSelf)
            {
                barView = _listHpBar[i];
                break;
            }
        }

        if (null == barView)
        {
            barView = Util.Clone(hpBarView, $"hp_bar_{_listHpBar.Count}");
            _listHpBar.Add(barView);
        }

        return barView;
    }

    private HpView GetHpView()
    {
        HpView hpview = null;
        for (var i = 0; i < _listHpView.Count; i++)
        {
            if (!_listHpView[i].gameObject.activeSelf)
            {
                hpview = _listHpView[i];
                break;
            }
        }

        if (null == hpview)
        {
            hpview = Util.Clone(hpView, $"hp_view_{_listHpView.Count}");
            _listHpView.Add(hpview);
        }

        return hpview;
    }

    private void UpdateHpAndShield()
    {
        if (BattleManager.Instance.Hero != null)
        {
            UpdateHp();
            UpdateShield();
        }
    }

    private int heroHp;
    private int heroShield;
    private int battleTime;
    private int extraCoin;
    private int rewardCoin;

    private void UpdateHp()
    {
        var hero = BattleManager.Instance.Hero;
        var heroHp = (int)Math.Ceiling(Math.Max(0, hero.Hp));
        if (this.heroHp != heroHp)
        {
            this.heroHp = heroHp;
            txt_hp.text = heroHp.ToString();
            slider_hp.value = hero.HPPercent;
        }

    }

    private void UpdateShield()
    {
        var hero = BattleManager.Instance.Hero;
        var heroShield = (int)Math.Ceiling(Math.Max(0, hero.Shield));
        if (this.heroShield != heroShield)
        {
            this.heroShield = heroShield;
            RefreshShield(heroShield, hero.ShieldPercent);
        }
    }

    private void RefreshShield(int shield, float shieldPercent)
    {
        txt_shield.text = Math.Max(0, shield).ToString();
        slider_shield.value = Math.Max(0, shieldPercent);
    }

    private void UpdateTime()
    {
#if UNITY_EDITOR
        float seconds = BattleManager.Instance.BattleTime;
        int hours = Mathf.FloorToInt(seconds / 3600);
        int minutes = Mathf.FloorToInt((seconds % 3600) / 60);
        int secs = Mathf.FloorToInt(seconds % 60);
        int milliSecs = Mathf.FloorToInt((seconds - Mathf.FloorToInt(seconds)) * 1000);

        txtTime.text = string.Format("{0:D2}:{1:D2}:{2:D2}:{3:D3}", hours, minutes, secs, milliSecs);
#endif
        if (!BattleManager.LevelModel)
        {
            var battleTime = (int)GoldLevelManager.Instance.BattleTime;
            if (this.battleTime != battleTime)
            {
                this.battleTime = battleTime;
                txtGoldLevelTime.text = battleTime.ToString();
            }

        }

    }

    private void OnExtraDropRewardCoin(params object[] args)
    {
        if (BattleManager.LevelModel)
        {
            var extraCoin = BattleManager.Instance.ExtraCoin;
            if (this.extraCoin != extraCoin)
            {
                this.extraCoin = extraCoin;
                txt_dropcoin.text = extraCoin.ToString();
            }

            if (_sequence != null) _sequence.Kill();
            _sequence = GetSequence(drop_coin.transform);
            _sequence.Play();

            if (_sequence2 != null) _sequence2.Kill();
            _sequence2 = GetSequence(txt_dropcoin.transform);
            _sequence2.Play();

        }
        else
        {
            var rewardCoin = GoldLevelManager.Instance.RewardCoin;
            if (this.rewardCoin != rewardCoin)
            {
                this.rewardCoin = rewardCoin;
                txt_coinleveldropcoin.text = rewardCoin.ToString();
            }

            if (_sequence != null) _sequence.Kill();
            _sequence = GetSequence(drop_coin.transform);
            _sequence.Play();

            if (_sequence2 != null) _sequence2.Kill();
            _sequence2 = GetSequence(txt_coinleveldropcoin.transform);
            _sequence2.Play();

        }

    }

    private void OnExtraDropRewardChip(params object[] args)
    {

        txt_dropitem.text = BattleManager.Instance.ExtraItem.ToShort();

        if (_sequence3 != null) _sequence3.Kill();
        _sequence3 = GetSequence(drop_item.transform);
        _sequence3.Play();

        if (_sequence4 != null) _sequence4.Kill();
        _sequence4 = GetSequence(txt_dropitem.transform);
        _sequence4.Play();
    }

    private Sequence GetSequence(Transform transform)
    {
        transform.transform.localScale = Vector3.one;
        var sequence = DOTween.Sequence();
        sequence.Append(transform.DOScale(Vector3.one * 1.5f, 0.15f).SetEase(Ease.OutQuad));
        sequence.Append(transform.DOScale(Vector3.one, 0.15f).SetEase(Ease.OutQuad));
        return sequence;
    }

    private void OnGetBuff(params object[] args)
    {
        var name = (string)args[0];
        var skillId = (int)args[1];
        foreach (var item in _goldLevelEquipItemList)
        {
            if (item.SkillId == skillId)
            {
                if (!BuffSkillIdNumMap.ContainsKey(skillId))
                {
                    BuffSkillIdNumMap[skillId] = 1;
                }
                if (BuffSkillIdNumMap.TryGetValue(skillId, out var num))
                {
                    if (num < ConstValue.GoldLevel_skill_up_num)
                    {
                        BuffSkillIdNumMap[skillId]++;
                        item.UpLevel(CurOrder, BuffSkillIdNumMap[skillId]);
                    }
                }
                break;
            }

        }

        // var rectTrans = (RectTransform)floatView.transform.parent;
        // var view = Util.Clone(buffTipsView, $"buffTips");
        // view.Show(GoldLevelManager.Instance.Hero, name);
        // var sequence = DOTween.Sequence();
        // sequence.Append(view.transform.DOScale(Vector3.one * 1.5f, 0.15f).SetEase(Ease.OutQuad));
        // sequence.Append(view.transform.DOScale(Vector3.one, 0.15f).SetEase(Ease.OutQuad));
        // sequence.Append(view.transform.DOScale(Vector3.one, 1f).SetEase(Ease.OutQuad));
        // sequence.onComplete += () => GameObject.Destroy(view.gameObject);

    }

    private void OnGoldLevelKillMonsterCount(params object[] args)
    {
        txt_GoldLevelCount.text = GoldLevelManager.Instance.GetTargetCount();
    }

    private void InitGoldLevelSkill()
    {
        GoldLevelEquipList.gameObject.SetActive(!BattleManager.LevelModel);

        if (BattleManager.LevelModel) return;

        GoldLevelItem.gameObject.SetActive(false);
        foreach (var item in _goldLevelEquipItemList)
        {
            Destroy(item.gameObject);
        }
        _goldLevelEquipItemList.Clear();

        for (var i = 0; i < GoldLevelManager.Instance.Equips.Count; i++)
        {
            var config = GoldLevelManager.Instance.Equips[i];
            GoldLevelEquipItem item = null;
            item = Util.Clone(GoldLevelItem, $"equipItem_{i}");
            item.gameObject.SetActive(true);
            item.Show(config.ID, i);
            item.GetComponentInChildren<Button>().enabled = false;
            _goldLevelEquipItemList.Add(item);
        }
    }
}
