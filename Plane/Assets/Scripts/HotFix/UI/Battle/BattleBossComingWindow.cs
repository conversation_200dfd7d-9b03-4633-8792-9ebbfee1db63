using System.Collections;
using System.Collections.Generic;
using GameCommon;
using Spine;
using Spine.Unity;
using UnityEngine;

public class BattleBossComingWindow : WindowBase
{
    [SerializeField] private SkeletonGraphic _spine;
    protected override void OnInit()
    {
        base.OnInit();
    }

    protected override void OnOpen()
    {
        base.OnOpen();
        AudioManager.PlaySound(EAudio.BossAppear);
        _spine.AnimationState.SetAnimation(0, "animation", false).Complete += OnPlayFinished;
    }

    private void OnPlayFinished( TrackEntry entry)
    {
        OnClickClose();
    }
}
