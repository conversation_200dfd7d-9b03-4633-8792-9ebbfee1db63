using System;using System.Collections;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using GameCommon;
using UnityEngine;
using UnityEngine.UI;

public enum EShopType
{
    DiscountShop,  // 特惠商店
    RandomShop,  // 武器盲盒
    ResourceShop  // 资源商店
}

public class ShopWindow : WindowBase
{
    public ResourceController res_gold;
    public ResourceController res_diamond;
    public DiscountShop discountShop;
    public RandomShop randomShop;
    public ResourceShop resourceShop;
    public ScrollRect scroll_shopGroup;
    public RectTransform rect_content;
    
    private EShopType _shopType;

    protected override void OnOpen()
    {
        base.OnOpen();

        _shopType = _initArgs.Length == 0 ? EShopType.DiscountShop : (EShopType)_initArgs[0];
        res_gold.Show((int)ECoinType.Gold);
        res_diamond.Show((int)ECoinType.Diamond);
        
        Show();
        StartCoroutine(TurnToShop());
        
        AddListener(EGameEvent.RefreshDiscount,OnRefreshDiscount);
        AddListener(EGameEvent.RandomShopLevelUp,OnRandomShopLevelUp);
        AddListener(EGameEvent.UpdateRandomShopProgress,OnUpdateRandomShopProgress);
        AddListener(EGameEvent.BuyRandomShopByAd,OnBuyRandomShopByAd);
        AddListener(EGameEvent.CheckGuide, OnCheckGuide);
    }

    private void Show()
    {
        discountShop.Show();
        randomShop.Show();
        resourceShop.Show();
    }

    private IEnumerator TurnToShop()
    {
        yield return new WaitForEndOfFrame();
        
        RectTransform targetItem = null;
        switch (_shopType)
        {
            case EShopType.DiscountShop:
                targetItem = discountShop.GetComponent<RectTransform>();
                break;
            case EShopType.RandomShop:
                targetItem = randomShop.GetComponent<RectTransform>();
                break;
            case EShopType.ResourceShop:
                targetItem = resourceShop.GetComponent<RectTransform>();
                break;
        }

        var targetPos = targetItem.localPosition;
        var contentHeight = rect_content.rect.height;
        // 计算所需的偏移量
        var offset = 1 - Math.Abs(targetPos.y / contentHeight);
        scroll_shopGroup.verticalNormalizedPosition = offset;
    }

    private void OnRefreshDiscount(params object[] args)
    {
        discountShop.Refresh();
    }
    
    private void OnUpdateRandomShopProgress(params object[] args)
    {
        randomShop.UpdateProgress();
    }
    
    private void OnRandomShopLevelUp(params object[] args)
    {
        randomShop.Show();
    }
    
    private void OnBuyRandomShopByAd(params object[] args)
    {
        if (args.Length < 1) return;
        var boxType = (int)args[0];
        randomShop.UpdateFreeState(boxType);
    }
    
    private void OnCheckGuide(params object[] args)
    {
        _ = CheckGuide();
    }


    protected override void OnClose()
    {
        base.OnClose();
        
        StopAllCoroutines();
    }
    
    private async UniTask CheckGuide()
    {
        if (UIManager.Instance.HasUpUI(CurOrder))
        {
            return;
        }
        
        if(!GuideData.IsGuideComplete(4) && EquipData.Instance.CheckHaveEquipCanUp())
        {
            if (GuideData.Instance.GuideStep == 0)
            {
                GuideData.StartGuide(4);
                UIManager.Instance.OpenWindow(EWindowName.Guide, EWindowLayer.Guide);
                await UniTask.WaitUntil(() => UIManager.IsOpen(EWindowName.Guide));
                GlobalEvent.DispatchEvent(EGameEvent.NavigationGuide);
            }
        }
    }
}
