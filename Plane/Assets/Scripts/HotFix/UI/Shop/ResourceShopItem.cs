using System;
using System.Collections;
using System.Collections.Generic;
using GameCommon;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class ResourceShopItem : MonoBehaviour
{
    [SerializeField] private Image img_icon;
    [SerializeField] private TextMeshProUGUI txt_Num;
    [SerializeField] private Button btn_buy;
    [SerializeField] private Image img_propIcon;
    [SerializeField] private TextMeshProUGUI txt_price;
    [SerializeField] private GameObject obj_sellout;
    [SerializeField] private Button btn_check;
    [SerializeField] private Image img_buy;

    private int _goodsId;
    private GoodsCfg _goodsCfg;

    private void Awake()
    {
        btn_buy.AddListener(OnClickBuy);
        btn_check.AddListener(OnClickCheck);
        GlobalEvent.AddEvent(EGameEvent.DayChange, UpdateState);
    }

    public void Show(int goodsId)
    {
        _goodsId = goodsId;
        _goodsCfg = GoodsConfig.Instance.Get(_goodsId);
        if (_goodsCfg == null) return;

        UpdateBasic();
        UpdateState();
    }

    private void UpdateBasic()
    {
        txt_Num.text = $"x{_goodsCfg.GoodsNum}";
        txt_price.text = _goodsCfg.BuyAd != 0 ? Util.GetLocalize("Shop_BtnText_Free") : $"{_goodsCfg.BuyNum}";
        var iconName = $"sd_jinbi_{_goodsCfg.ManualRefreshCost}";
        var propIconAtlas = "";
        var propIconName = "";
        if (_goodsCfg.BuyAd != 0)
        {
            propIconAtlas = "common";
            propIconName = "ty_iocn_guanggao";
        }
        else
        {
            var propCfg = PropConfig.Instance.Get(_goodsCfg.BuyType);
            if (propCfg != null)
            {
                propIconAtlas = propCfg.iconAtlas;
                propIconName = propCfg.iconUrl;
            }
        }

        ResourceManager.LoadSprite(img_icon, "shop", iconName);
        ResourceManager.LoadSprite(img_propIcon, propIconAtlas, propIconName);

        if (_goodsCfg.BuyAd == 0)
        {
            ResourceManager.LoadSprite(img_buy, "common", "ty_anniu_huang");
            txt_price.color = new Color(106f / 255f, 35f / 255f, 0, 1);
        }
        else
        {
            ResourceManager.LoadSprite(img_buy, "common", "ty_anniu_zi");
            txt_price.color = new Color(12f / 255f, 45 / 255f, 106f / 255f, 1);
        }
    }

    private void UpdateState(params object[] args)
    {
        if (_goodsCfg.BuyAd == 0) return;
        var adCfg = RewardedVideoConfig.Instance.Get(_goodsCfg.BuyAd);
        if (adCfg == null) return;
        var timesId = adCfg.limit;
        var remainTimes = TimesData.GetTimes(timesId);
        txt_price.text = Util.GetLocalize("Shop_BtnText_Free2", remainTimes);
        obj_sellout.SetActiveOptimize(remainTimes <= 0);
    }

    private void OnClickBuy()
    {
        AudioManager.ClickSound();
        if (_goodsCfg.BuyAd != 0)
        {
            ADManager.Instance.ShowRewardAd(_goodsCfg.BuyAd, ShowAdCallback);
        }
        else
        {
            var isEnough = BagData.Instance.IsEnough(_goodsCfg.BuyType, _goodsCfg.BuyNum);
            if (!isEnough)
            {
                Util.FloatTips("Common_PropLack");
                return;
            }

            ShopData.Instance.BuyResourceGoods(_goodsId);
        }
    }

    private void ShowAdCallback(bool isSuccess)
    {
        if (isSuccess)
        {
            ShopData.Instance.BuyResourceGoods(_goodsId);
            UpdateState();
        }
    }

    private void OnClickCheck()
    {
        AudioManager.ClickSound();
        UIManager.Instance.OpenWindow(EWindowName.PropDetails, args: _goodsCfg.GoodsType);
    }
}
