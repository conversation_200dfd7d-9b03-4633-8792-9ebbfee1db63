using System.Collections;
using System.Collections.Generic;
using GameCommon;
using Spine.Unity;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class RandomShopPop : WindowBase
{
    [SerializeField] private Button btn_mask;
    [SerializeField] private Button btn_close;
    [SerializeField] private TextMeshProUGUI txt_boxName;
    [SerializeField] private SkeletonGraphic spine_box;
    [SerializeField] private Button btn_free;
    [SerializeField] private Button btn_buy;
    [SerializeField] private Image img_propIcon;
    [SerializeField] private TextMeshProUGUI txt_price;
    [SerializeField] private RewardItemView rewardItem;
    
    private int _boxId;
    private GoodsCfg _goodsCfg;
    private EquipBoxLevelCfg _boxCfg;
    private List<RewardItemView> _rewardItemList = new List<RewardItemView>();
    
    protected override void OnInit()
    {
        base.OnInit();

        btn_mask.AddListener(Close);
        btn_close.AddListener(Close);
        btn_free.AddListener(OnClickFree);
        btn_buy.AddListener(OnClickBuy);
    }

    protected override void OnOpen()
    {
        base.OnOpen();

        if (_initArgs.Length < 1)
        {
            Debug.LogError("打开武器盲盒预览弹窗参数传递有误!");
            return;
        }

        _boxId = (int)_initArgs[0];
        _boxCfg = ShopData.Instance.GetEquipBoxCfgByBoxId(_boxId);
        _goodsCfg = ShopData.Instance.GetGoodsCfgByGoodsType(_boxId);
        
        UpdateBoxInfo();
        UpdateRewardItem();
        UpdateFreeState();
        
        AddListener(EGameEvent.BuyRandomShopByAd,UpdateFreeState);
        AddListener(EGameEvent.RandomShopLevelUp,OnLevelUp);
    }

    // 更新标题,icon,价格 等
    private void UpdateBoxInfo()
    {
        var boxNameKey = _boxCfg.BoxType == 1 ? "EquipBoxName_1" : "EquipBoxName_2";
        var animName = _boxCfg.BoxType == 1 ? "open_gold" : "open_gold2";
        var propCfg = PropConfig.Instance.Get(_goodsCfg.BuyType);
        
        txt_boxName.text = Util.GetLocalize(boxNameKey);
        txt_price.text = _goodsCfg.BuyNum.ToShort();
        spine_box.AnimationState.SetAnimation(0, animName, true);
        //ResourceManager.LoadSprite(img_boxIcon,"shop",iconName);
        ResourceManager.LoadSprite(img_propIcon,propCfg.iconAtlas,propCfg.iconUrl);
    }

    // 更新对应宝箱当中的奖励
    private void UpdateRewardItem()
    {
        foreach (var rewardItemView in _rewardItemList)
        {
            rewardItemView.gameObject.SetActiveOptimize(false);
        }

        var rewardIdArr = _boxCfg.Rewards;
        var rewardNumArr = _boxCfg.RewardNum;
        for (var i = 0; i < rewardIdArr.Length; i++)
        {
            RewardItemView rewardItem = null;
            if (i < _rewardItemList.Count)
            {
                rewardItem = _rewardItemList[i];
            }
            else
            {
                rewardItem = Util.Clone(this.rewardItem, $"rewardItem_{i}");
                _rewardItemList.Add(rewardItem);
            }
            
            rewardItem.Show(rewardIdArr[i],rewardNumArr[i], _jumpCall:()=>{
                            UIManager.Instance.CloseWindow(EWindowName.RandomShop);
                            UIManager.Instance.CloseWindow(EWindowName.PropDetails);
            });
        }
    }
    
    // 更新免费的状态
    private void UpdateFreeState(params object[] args)
    {
        var isFree = _boxCfg.BoxType == 1 ? ShopData.Instance.IsFreeNormal : ShopData.Instance.IsFreeSenior;
        btn_free.gameObject.SetActiveOptimize(isFree);
    }

    private void OnLevelUp(params object[] args)
    {
        var curLevel = ShopData.Instance.CurLevel;
        _boxId = _boxCfg.BoxType == 1? ShopData.Instance.GetNormalBoxId(curLevel) : ShopData.Instance.GetSeniorBoxId(curLevel);
        _boxCfg = ShopData.Instance.GetEquipBoxCfgByBoxId(_boxId);
        _goodsCfg = ShopData.Instance.GetGoodsCfgByGoodsType(_boxId);
        
        UpdateRewardItem();
        UpdateFreeState();
    }

    private void OnClickFree()
    {
        AudioManager.ClickSound();
        ADManager.Instance.ShowRewardAd(_goodsCfg.BuyAd,ShowAdCallback);
    }

    private void ShowAdCallback(bool isSuccess)
    {
        if (isSuccess)
        {
            ShopData.Instance.BuyRandomGoods(EBuyType.Ad,_boxId);
        }
    }
    
    private void OnClickBuy()
    {
        AudioManager.ClickSound();
        var isEnough = BagData.Instance.IsEnough(_goodsCfg.BuyType, _goodsCfg.BuyNum);
        if (!isEnough)
        {
            Util.FloatTips("Common_PropLack");
            return;
        }

        ShopData.Instance.BuyRandomGoods(EBuyType.Diamond,_boxId);
    }
}
