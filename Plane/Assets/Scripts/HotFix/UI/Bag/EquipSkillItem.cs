using System.Collections;
using System.Collections.Generic;
using GameCommon;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class EquipSkillItem : MonoBehaviour
{
    [SerializeField] private Image img_bg;
    [SerializeField] private TextMeshProUGUI txt_desc;
    [SerializeField] private GameObject obj_mask;
    [SerializeField] private TextMeshProUGUI txt_unlockDesc;
    
    public void Show(EquipUpgradeCfg upgradeCfg,int curLevel,bool unlockTip = false)
    {
        gameObject.SetActiveOptimize(true);
        if (!unlockTip)
        {
            int quality = BuffConfig.Instance.Get(upgradeCfg.UpgradeUnlockBuff).Quality;
            ResourceManager.LoadSprite(img_bg, "bag", $"bb_mb_{quality}");
        }
        else
        {
            ResourceManager.LoadSprite(img_bg, "bag", $"bb_mb_5");
        }
        txt_desc.text = Util.GetLocalize(upgradeCfg.UpgradeUnlockDesc);
        var isUnlock = curLevel >= upgradeCfg.Level;
        txt_unlockDesc.text = Util.GetLocalize("Common_UnlockDescLv",upgradeCfg.Level);
        obj_mask.SetActiveOptimize(!isUnlock);
    }
}
