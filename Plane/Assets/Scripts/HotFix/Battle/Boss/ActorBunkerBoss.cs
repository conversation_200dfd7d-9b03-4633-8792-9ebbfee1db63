using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using Battle;
using GameCommon;
using Spine;
using Spine.Unity;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// ���еﱤBoss
    /// </summary>
    public class ActorBunkerBoss : ActorBoss
    {
        [SerializeField]
        protected Transform mainMuzzle;
        [SerializeField]
        protected GameObject attackEffObj;
        [SerializeField]
        protected Transform gunTrans;
        [SerializeField]
        protected SkeletonAnimation gunSpine;

        public Transform MainMuzzle => mainMuzzle;

        private string moveAnimName = "crawl";
        private string gunIdleName = "walk_up";

        private float effectTime = 0.5f;
        private float timer = 0;
        private bool isRotate = false;
        private float targetAngle;
        private float rotateSpeed;

        protected override void InitShow()
        {
            base.InitShow();

            //GlobalEvent.AddEvent(EGameEvent.BattleMapStop, MapRollEnd);

            //if (!BattleRoot.Instance.IsMapRollEnd)
            //{
            //    idleAnimName = moveAnimName;
            //    PlayAnim(0, idleAnimName, true);
            //}
            gunSpine.AnimationState.SetAnimation(0, gunIdleName, true);
            attackEffObj.SetActiveOptimize(false);
            isRotate = false;
            targetAngle = 0;
            rotateSpeed = 0;
        }

        public override void Hide()
        {
            base.Hide();

            //GlobalEvent.RemoveEvent(EGameEvent.BattleMapStop, MapRollEnd);
        }

        protected override void OnUpdate()
        {
            base.OnUpdate();

            if (attackEffObj.activeSelf)
            {
                timer += Time.deltaTime;
                if (timer >= effectTime)
                {
                    timer = 0;
                    attackEffObj.SetActiveOptimize(false);
                }
            }
        }

        protected override void UpdateAI()
        {
            if (isRotate && rotateSpeed != 0 && !CheckHasState(EActorState.Dead))
            {
                var curAngle = gunTrans.eulerAngles.z;
                if (curAngle > 180)
                {
                    curAngle -= 360;
                }
                if ((rotateSpeed > 0 && curAngle < targetAngle) || (rotateSpeed < 0 && curAngle > targetAngle))
                {
                    curAngle += rotateSpeed;
                    gunTrans.eulerAngles = new Vector3(0, 0, curAngle);
                }

                if ((rotateSpeed > 0 && curAngle >= targetAngle) || (rotateSpeed < 0 && curAngle <= targetAngle))
                {
                    isRotate = false;
                    gunTrans.eulerAngles = new Vector3(0, 0, targetAngle);
                }
            }
        }

        protected override void OnHit(GameObject obj)
        {
        }

        protected override void OnOut(GameObject obj)
        {
        }


        private void MapRollEnd(params object[] args)
        {
            //if (spine.AnimationState.GetCurrent(0).Animation.Name == idleAnimName)
            //{

            //}
            //else
            //{
            //    idleAnimName = "walk";
            //}
            idleAnimName = "walk";
            PlayIdleAnim();
        }

        public void SetRotateToHero(bool isLook)
        {
            if (isLook)
            {
                var tempDir = BattleManager.Instance.GetHeroPos() - gunTrans.position;
                float heroAngle = Mathf.Rad2Deg * Mathf.Atan2(tempDir.y, tempDir.x) + 90f;
                //Debug.LogError("heroAngle:" + heroAngle);
                heroAngle = Mathf.Clamp(heroAngle, -20, 20);

                RotateToAngle(heroAngle);
            }
            else
            {
                RotateToAngle(0);
            }
        }

        private void RotateToAngle(float angle)
        {
            targetAngle = angle;
            var curAngle = gunTrans.eulerAngles.z;
            if (curAngle > 180)
            {
                curAngle -= 360;
            }
            //Debug.LogError("curAngle:" + curAngle + "    target:" + targetAngle);
            if (curAngle > targetAngle)
            {
                isRotate = true;
                //rotateSpeed = -1;
                rotateSpeed = -Attr.GetAttr(EAttr.TurnSpeed);
            }
            else if (curAngle < targetAngle)
            {
                isRotate = true;
                //rotateSpeed = 1;
                rotateSpeed = Attr.GetAttr(EAttr.TurnSpeed);
            }
            else
            {
                isRotate = false;
                rotateSpeed = 0;
            }
        }

        public void SetCurAngle(Vector3 dir)
        {
            isRotate = false;
            float angle = Mathf.Rad2Deg * Mathf.Atan2(dir.y, dir.x) + 90f;
            gunTrans.eulerAngles = new Vector3(0, 0, angle);
        }

        public override void PlayAttackAnim()
        {
            //base.PlayAttackAnim();
            gunSpine.AnimationState.SetAnimation(0, "attack", false);
            gunSpine.AnimationState.AddAnimation(0, gunIdleName, true, 0);
            attackEffObj.SetActiveOptimize(true);
            timer = 0;
        }

        public override void PlaySkillAnim()
        {
            //base.PlaySkillAnim();
            gunSpine.AnimationState.SetAnimation(0, "skill", false);
            //gunSpine.AnimationState.AddAnimation(0, gunIdleName, true, 0);
        }

        public void PlayGunIdle()
        {
            gunSpine.AnimationState.SetAnimation(0, gunIdleName, true);
        }
        public override void Dead(bool isHit)
        {
            base.Dead(isHit);

            gunSpine.gameObject.SetActive(false);
        }
    }
}