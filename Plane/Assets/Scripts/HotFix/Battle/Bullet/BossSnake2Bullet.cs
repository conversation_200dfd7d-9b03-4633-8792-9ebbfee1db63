using GameCommon;
using UnityEngine;

namespace Battle
{
    public class BossSnake2Bullet:BulletBase
    {
        private float _duration;
        private float _timer;
        private float _interval;
        private float _intervalTime;
        private ActorHeroBase _hero;
        

        public override void Show(Vector3 pos, Vector3 speed, SkillBase skill, bool isSonSkill = false)
        {
            base.Show(pos, speed, skill, isSonSkill);
            _duration = skill.Cfg.AtkDuration * 0.01f;
            _timer = 0;
            transform.localPosition=Vector3.zero;
            transform.localEulerAngles = new Vector3(0, 0, -90);

            _interval = skill.GetAttr(EAttr.AtkSpeed);
        }

        protected override void OnUpdate()
        {
            _timer += Time.deltaTime;
            if (_timer>_duration)
            {
                Hide();
                _timer = 0;
            }

            _intervalTime -= Time.deltaTime;
            if (_intervalTime < 0)
            {
                _intervalTime = _interval;
                DoHurt();
            }
        }

        protected override void OnHit(GameObject obj)
        {
            var heroBase = obj.GetComponent<ActorHeroBase>();
            if (heroBase != null)
            {
                _hero = heroBase;
                DoHurt();
            }
        }
        
        protected override void OnOut(GameObject obj)
        {
            var heroBase = obj.GetComponent<ActorHeroBase>();
            if (heroBase != null)
            {
                _hero = null;
            }
        }

        private void DoHurt()
        {
            if (_hero != null)
            {
                _hero.Hurt(_skill.GetDamage(_hero));
                
                if (!string.IsNullOrEmpty(_skill.Cfg.AudioHit))
                {
                    AudioManager.PlaySound(_skill.Cfg.AudioHit,0.1f);
                }
            }
        }
    }
}