using Battle;
using GameCommon;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// ���˲�ֵλ���ӵ�
    /// </summary>
    public class EnemyLerpPosBullet : EnemyBullet
    {
        [SerializeField]
        protected AnimationCurve lerpCurve;

        protected Vector3 targetPos;
        protected float lerpTime;
        protected float delayShowTime;
        protected float timer;

        protected Vector3 diffPos;

        protected bool isShowSpr;

        public void SetCurPos(Vector3 pos, float lerpTime, float delayShowTime)
        {
            targetPos = _curPos;
            this.delayShowTime = delayShowTime;
            this.lerpTime = lerpTime;
            timer = 0;
            SetPos(pos);

            isShowSpr = false;
            spr.gameObject.SetActiveOptimize(false);
        }

        protected override void OnUpdate()
        {
            targetPos += _speed;

            if (!isShowSpr)
            {
                timer += Time.deltaTime;
                if (timer >= delayShowTime)
                {
                    isShowSpr = true;
                    spr.gameObject.SetActiveOptimize(true);
                    timer = 0;
                    diffPos = targetPos - _curPos;
                }
            }
            else
            {
                if (timer < lerpTime)
                {
                    timer += Time.deltaTime;
                    if (timer > lerpTime)
                    {
                        timer = lerpTime;
                    }
                }

                SetPos(targetPos - Vector3.Lerp(diffPos, Vector3.zero, lerpCurve.Evaluate(timer / lerpTime)));
            }
            
        }
    }
}
