using System;
using System.Collections;
using System.Collections.Generic;
using GameCommon;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Battle
{
    //夹子
    public class BulletFolder : BulletBase
    {
        private Vector3 startPos;
        private Vector3 midPos;
        private Vector3 targetPos;
        private ActorEnemyBase actorTarget;
        private float _time;
        private float _duration;

        public void SetTargetPos(Vector3 pos)
        {
            startPos = transform.position;
            targetPos = pos;
            targetPos.z = startPos.z;
            actorTarget = _skill.GetTarget(distance:999);
            if (actorTarget != null)
            {
                targetPos = actorTarget.WorldPos;
            }
            
            var dis = Vector3.Cross(targetPos - startPos,Vector3.back);
            var offset = Random.Range(0.5f, 0.5f);
            midPos = startPos + (targetPos - startPos) * Random.Range(0.4f, -0.7f) + dis * offset;
            _duration = Vector2.Distance(startPos, targetPos) * (1 / _skill.GetAttr(EAttr.AtkSpeed));
            _time = 0;
        }

      

        protected override void OnUpdate()
        {
            if ( Time.deltaTime > 0)
            {
                if (!actorTarget || !actorTarget.IsAlive)
                {
                    actorTarget = _skill.GetTarget(distance:999);
                    if (actorTarget)
                    {
                        startPos = transform.position;
                        targetPos = actorTarget.WorldPos;
                        var dis = Vector3.Cross(targetPos - startPos,Vector3.back);
                        var offset = Random.Range(0.5f, 0.5f);
                        midPos = startPos + (targetPos - startPos) * Random.Range(0.4f, -0.7f) + dis * offset;
                        _duration = Vector2.Distance(startPos, targetPos) * (1 / _skill.GetAttr(EAttr.AtkSpeed));
                        _time = 0;
                    }
                }

                if (actorTarget)
                {
                    targetPos = actorTarget.WorldPos;
                }
                
                _time += Time.deltaTime;
                _curPos = Util.BezierPoint(_time / _duration,startPos, midPos, targetPos);
                _dir = _curPos - _prePos;
                _prePos = _curPos;
                var angel = Mathf.Rad2Deg * Mathf.Atan2(_dir.y, _dir.x) - 90f;
                transform.localEulerAngles = new Vector3(0, 0, angel);
                transform.position = _curPos;
                    
                if (_time > _duration + 0.1f)
                {
                    DoBoom();
                }
            }  
        }

        /// <summary>
        /// 爆炸
        /// </summary>
        private void DoBoom()
        {
            EffectManager.Instance.PlayWorldEffect("eff_yan_attack1_hit", BattleRoot.Instance.ActorRoot,
                transform.position, scale: _skill.GetAttr(EAttr.AtkDistance));
            
            var listEnemy = BattleActorManager.Instance.GetAreaEnemy(transform.position, _skill.GetAttr(EAttr.AtkDistance));
            for (int i = 0; i < listEnemy.Count; i++)
            {
                listEnemy[i].Hurt(_skill.GetDamage(listEnemy[i]));
            }
            
            if (!string.IsNullOrEmpty(_skill.Cfg.AudioHit))
            {
                AudioManager.PlaySound(_skill.Cfg.AudioHit,0.1f);
            }
            
            Hide();
        }

        protected override void OnHit(GameObject obj)
        {
           
        }
    }
}

