using GameCommon;
using UnityEngine;

namespace Battle
{
    public class ShoeBase : Mono<PERSON><PERSON>aviour
    {
        public SpriteRenderer spr;

        protected SkillBase _skill;
        protected float _distance;
        protected float _curDistance;
        protected Vector3 _dir;
        protected Vector3 _startPos;
        protected Vector3 _rot;
        protected bool _isOut;

        private const float ROT = 10f;
        
        
        public void Show(Vector3 dir,Vector3 pos, SkillBase skill)
        {
            _skill = skill;
            _dir = dir;
            _distance = _skill.GetAttr(EAttr.AtkDistance);
            _isOut = true;

            _startPos = pos;
            _rot = Vector3.zero;
            transform.position = pos;
            transform.eulerAngles = _rot;

            ResourceManager.LoadSprite(spr, "battle_icon",_skill.Cfg.Icon);
          
            gameObject.SetActive(true);
        }

        public void Hide()
        {
            BattleObjectPool.Release(gameObject);
        }

        private void Update()
        {
            if (Time.deltaTime > 0)
            {
                OnUpdate();
            }
        }

        protected virtual void OnUpdate()
        {
            _rot.z += ROT;
            transform.localEulerAngles = _rot;
            if (_isOut)//出去
            {
                _curDistance = Vector2.Distance(_startPos, transform.position);
                if (_curDistance <= _distance)
                {
                    transform.position += _dir;
                }
                else
                {
                    _isOut = false;
                }
            }
            else//回来
            {
                transform.position -= _dir;
                if (!BattleRoot.Instance.InRect(transform.position))
                {
                    Hide();
                }
            }
           
        }


        /// <summary>
        /// 当碰撞到敌方时
        /// </summary>
        /// <param name="col"></param>
        private void OnTriggerEnter2D(Collider2D col)
        {
            OnHit(col.gameObject);
        }

        private void OnTriggerExit2D(Collider2D other)
        {
           
        }

        /// <summary>
        /// 当攻击到目标
        /// </summary>
        /// <param name="obj"></param>
        protected virtual void OnHit(GameObject obj)
        {
            var target = obj.GetComponent<ActorEnemyBase>();
            if (target && null != _skill)
            {
                target.Hurt(_skill.GetDamage(target));
                
                if (!string.IsNullOrEmpty(_skill.Cfg.AudioHit))
                {
                    AudioManager.PlaySound(_skill.Cfg.AudioHit,0.1f);
                }
            }

            if (!_isOut)
            {
                var hero = obj.GetComponent<ActorHeroBase>();
                if (hero)//撞到自己了
                {
                    _startPos = transform.position;
                    _isOut = true;
                }
            }
        }
    }
}