using System.Collections.Generic;
using UnityEngine;

namespace Battle
{
    public class BattleSquareMove : BattleMoveBase
    {
        protected List<Vector3> waypoints;
        protected int index;

        /// <summary>
        /// 设置椭圆数据
        /// </summary>
        /// <param name="sideLength">边长</param>
        public void SetSquareData(float sideLength)
        {
            if (waypoints == null)
            {
                waypoints = new List<Vector3>();
            }
            else
            {
                waypoints.Clear();
            }
            var curPos = actor.WorldPos;
            waypoints.Add(curPos);
            if (curPos.x >= 0)
            {
                waypoints.Add(new Vector3(curPos.x - sideLength, curPos.y));
                waypoints.Add(new Vector3(curPos.x - sideLength, curPos.y + sideLength));
                waypoints.Add(new Vector3(curPos.x, curPos.y + sideLength));
            }
            else
            {
                waypoints.Add(new Vector3(curPos.x + sideLength, curPos.y));
                waypoints.Add(new Vector3(curPos.x + sideLength, curPos.y + sideLength));
                waypoints.Add(new Vector3(curPos.x, curPos.y + sideLength));
            }
            index = 1;
        }

        protected override void OnUpdateMove()
        {
            base.OnUpdateMove();
            var curPos = actor.WorldPos;
            // 计算物体当前位置和目标位置之间的距离
            float distance = Vector3.Distance(curPos, waypoints[index]);
            // 如果物体接近目标位置，切换到下一个顶点
            if (distance < 0.1f)
            {
                index = (index + 1) % waypoints.Count;
            }
            actor.SetPos(Vector3.MoveTowards(curPos, waypoints[index], actor.Speed * Time.timeScale));
        }

        public override void Release()
        {
            base.Release();

            if (waypoints != null)
            {
                waypoints.Clear();
            }

            BattleClassPool.Instance.ReleaseObject(this);
        }
    }
}
