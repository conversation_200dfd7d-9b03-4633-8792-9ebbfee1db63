using System;
using System.Collections;
using System.Collections.Generic;
using Coffee.UIEffects;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using GameCommon;
using Spine.Unity;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// 精英怪基础类
    /// </summary>
    public class ActorEliteEnemyBase : ActorEnemyBase
    {

        protected override void InitShow()
        {
            base.InitShow();

        }

        public override void Dead(bool isHit)
        {
            if (CheckHasState(EActorState.Dead))
            {
                return;
            }

            box.enabled = false;
            _state = EActorState.Dead;

            CheckDeadSkill();
            ClearSkillAndBuff();
            CreateDeadEffect();

            AudioManager.PlaySound(EAudio.BossDie);
            spine.gameObject.SetActiveOptimize(false);
            BattleRoot.Instance.Shake(_cfg.DeadShakeTime * 0.01f);
            if (!isHit)
            {
                CreateDeadReward(true);
            }
            GlobalEvent.DispatchEvent(EGameEvent.BattleDead, _insID, false);
            // _ = WaitDie();
        }

        // protected virtual async UniTask WaitDie()
        // {
        //     await UniTask.Delay(2000, true);
        //     int count;
        //     if (BattleActorManager.Instance.CheckIsLoopEnemy(InsID))
        //         count = BattleActorManager.Instance.GetEnemyCount(true);
        //     else
        //         count = BattleActorManager.Instance.GetOnlyInEnemyDicCount();
        //     bool isDrop = count > 1;
        //     CreateDeadReward(isDrop);
        //     GlobalEvent.DispatchEvent(EGameEvent.BattleDead, _insID, false);
        // }
    }
}

