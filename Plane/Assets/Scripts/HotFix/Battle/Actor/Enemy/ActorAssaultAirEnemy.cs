using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// 突袭飞机
    /// </summary>
    public class ActorAssaultAirEnemy : ActorEnemyBase
    {
        enum AIType
        {
            MoveToPos = 1,  //移动到目标点
            MoveAndSprint,  //移动后突袭
        }

        private AIType aiType;

        private float waitTime = 1;
        private float waitTimer;

        protected override void InitShow()
        {
            base.InitShow();
            aiType = (AIType)_cfg.AIType;
            waitTimer = 0;
        }

        protected override void OnAdmissionMoveEnd()
        {
            SetDir(Vector3.down);
            base.OnAdmissionMoveEnd();
        }

        protected override void UpdateAI()
        {
            if (isAdmissionMoveEnd && aiType == AIType.MoveAndSprint) 
            {
                if (waitTimer < waitTime)
                {
                    waitTimer += Time.deltaTime;
                }
                else
                {
                    if (!moveCom.IsMoving)
                    {
                        _speed = _speed * 2;
                        moveCom.LineMove(BattleManager.Instance.GetHeroPos() - _curPos);
                    }
                }
            }
        }

        protected override void OnHit(GameObject obj)
        {
            var target = obj.GetComponent<ActorHeroBase>();
            if (target)
            {
                IsReach = true;
                _baseSkill?.Cast();
                _baseSkill?.InitCD();

                if (aiType == AIType.MoveAndSprint) 
                {
                    Dead(true);
                }
            }
        }
    }
}
