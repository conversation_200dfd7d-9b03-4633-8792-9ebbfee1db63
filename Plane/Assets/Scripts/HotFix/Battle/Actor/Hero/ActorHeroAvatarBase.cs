using GameCommon;
using Spine.Unity;
using System;
using System.Collections.Generic;
using UnityEngine;

namespace Battle
{
    public class ActorHeroAvatarBase : ActorHeroBase
    {
        private Vector3 diffPos;

        public override void Show(int cfgID, int insID, Vector3 pos, Attribute add)
        {
            base.Show(cfgID, insID, pos, add);

            if (BattleManager.LevelModel)
            {
                diffPos = _curPos - BattleManager.Instance.GetHeroPos();
            }
            else
            {
                diffPos = _curPos - GoldLevelManager.Instance.GetHeroPos();
            }

            comWingman.OnBattleStart();
        }

        public override void RefreshSkillAttr(bool isInit = false)
        {
            if (isInit)
            {
                isInitSkillAttr = true;
                _hpMax = _attr.GetAttr(EAttr.MaxHp);
                SetHp(_hpMax);

                _shield = _attr.GetAttr(EAttr.Shield);
                SetShield(_shield);
            }
            else
            {
                _hpMax = _attr.GetAttr(EAttr.MaxHp);
                SetHp(_hp);
                SetShield(_shield);
            }
        }

        protected override void OnUpdate()
        {
            if (BattleManager.LevelModel)
            {
                SetPos(BattleManager.Instance.GetHeroPos() + diffPos);
            }
            else
            {
                SetPos(GoldLevelManager.Instance.GetHeroPos() + diffPos);
            }

            base.OnUpdate();
        }

        public override void SetPos(Vector3 pos)
        {
            _curPos = pos;
            _dir = _curPos - _prePos;
            _prePos = _curPos;
            transform.position = _curPos;
            SetSpineDir(_dir);
        }

        public override void Hurt(DamageData damage)
        {

        }

        public override void Dead(bool isHit)
        {
            Hide();
        }
    }
}