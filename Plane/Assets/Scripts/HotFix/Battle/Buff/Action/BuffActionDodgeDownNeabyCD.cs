using GameCommon;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// ����ʱ�����ٶ�����50%������3�룬��ȴ5��
    /// </summary>
    public class BuffActionDodgeDownNeabyCD : BuffActionBase
    {
        private float effectDurationTimer = 0;
        private float effectDuration = 3f;

        private AttributeData attributeData;

        protected override void OnInit()
        {
            base.OnInit();

            attributeData = new AttributeData((int)EAttr.ColdDownFinalAdd, -5000);
            effectDurationTimer = effectDuration;
            GlobalEvent.AddEvent(EGameEvent.BattleHeroDodge, OnDodge);
        }

        protected override void OnClear()
        {
            base.OnClear();
            attributeData = null;
            GlobalEvent.RemoveEvent(EGameEvent.BattleHeroDodge, OnDodge);
        }

        protected override void OnUpdate()
        {
            base.OnUpdate();

            if(effectDurationTimer < effectDuration)
            {
                effectDurationTimer += Time.deltaTime;
                EffectEnd();
            }
        }

        private void OnDodge(params object[] args)
        {
            if (IsReady)
            {
                OnCast();
            }
        }

        protected override void OnCast()
        {
            base.OnCast();

            var skillList = targetActor.SkillList;
            foreach (var skill in skillList)
            {
                skill.AddAttr(attributeData);
            }

            effectDurationTimer = 0;
        }

        private void EffectEnd() 
        {
            var skillList = targetActor.SkillList;
            foreach (var skill in skillList)
            {
                skill.RemoveAttr(attributeData);
            }
        }
    }
}
