using Cysharp.Threading.Tasks;
using GameCommon;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices.ComTypes;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// �Էɻ����෢��㣬���Ҹ�����n���ӵ���ÿ�����{�������}�����angle �������·������Ա�ƫת
    /// </summary>
    public class EnemySkillDoubleScattering : SkillBase
    {
        public EnemySkillDoubleScattering(int cfgID, ActorBase actor) : base(cfg<PERSON>, actor)
        {
        }

        protected override void OnSkillStart()
        {
            base.OnSkillStart();

            _ = CreateBullet();
        }

        private async UniTask CreateBullet()
        {
            ActorEnemyBase enemy = Actor as ActorEnemyBase;
            if (enemy != null)
            {
                Transform leftMuzzle = enemy.LeftMuzzle;
                Transform rightMuzzle = enemy.RightMuzzle;

                enemy.PlayAttackAnim();

                var bulletSpeed = GetAttr(EAttr.Speed);
                var num = GetAttrInt(EAttr.Num);
                var atkInterval = GetAttr(EAttr.AtkInterval);

                for (int i = 0; i < num; i++)
                {
                    for (int j = 0; j < 2; j++)
                    {
                        bool isLeft = (j % 2 == 0);

                        Quaternion newRotation = Quaternion.AngleAxis(isLeft ? -Cfg.Angle * i : Cfg.Angle * i, Vector3.forward);
                        var speed = newRotation * Vector3.down * bulletSpeed;
                       
                        BattleObjectPool.Get(_cfg.Res, BattleRoot.Instance.ActorRoot, obj =>
                        {
                            var bullet = obj.GetComponent<EnemyBullet>();
                            if (bullet)
                            {
                                bullet.Show(isLeft ? leftMuzzle.position : rightMuzzle.position, speed, this);
                            }
                            else
                            {
                                BattleObjectPool.Release(obj);
                            }
                        }, _cfg.Scale * 0.01f, false);
                        
                        await UniTask.DelayFrame(1, cancellationToken: _actor.CancellationToken);
                    }


                    if (!string.IsNullOrEmpty(_cfg.AudioCast))
                    {
                        AudioManager.PlaySound(_cfg.AudioCast);
                    }

                    await UniTask.Delay((int)(atkInterval * 1000), cancellationToken: _actor.CancellationToken);
                }
            }
        }
    }
}