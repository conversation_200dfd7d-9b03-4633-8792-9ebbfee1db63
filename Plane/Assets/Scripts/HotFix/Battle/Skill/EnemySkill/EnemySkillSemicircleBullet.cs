using Cysharp.Threading.Tasks;
using GameCommon;
using System.Diagnostics;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// 半圆弹幕
    /// </summary>
    public class EnemySkillSemicircleBullet : SkillBase
    {
        public EnemySkillSemicircleBullet(int cfgID, ActorBase actor) : base(cfg<PERSON>, actor)
        {
        }

        protected override void OnSkillStart()
        {
            _ = CreateBullet();
        }

        private async UniTask CreateBullet()
        {
            ActorEnemyBase enemy = Actor as ActorEnemyBase;
            if (enemy != null)
            {
                enemy.PlayAttackAnim();

                var bulletSpeed = GetAttr(EAttr.Speed);
                var num = GetAttrInt(EAttr.Num);
                var atkInterval = GetAttr(EAttr.AtkInterval);

                var startPos = Actor.AttackPos;
                float offsetDis = 0.3f;

                for (int i = 0; i < num; i++)
                {
                    int index = i;
                    int startFrame = Time.frameCount;
                    for (int j = 0; j < 2; j++) 
                    {
                        bool isLeft = j == 0;
                        await UniTask.WaitForEndOfFrame(GameManager.Instance, _actor.CancellationToken);
                        BattleObjectPool.Get(Cfg.Res, BattleRoot.Instance.ActorRoot, obj =>
                        {
                            var bullet = obj.GetComponent<EnemyBullet>();
                            if (bullet)
                            {
                                Vector3 shotPos = startPos + (isLeft ? -1 : 1) * offsetDis * (index + 0.5f) * Vector3.right;
                                shotPos += Vector3.down * (bulletSpeed * (Time.frameCount - startFrame) * Time.timeScale);
                                bullet.Show(shotPos, Vector3.down * bulletSpeed, this);
                            }
                            else
                            {
                                BattleObjectPool.Release(obj);
                            }
                        }, Cfg.Scale * 0.01f, false);
                        
                        await UniTask.DelayFrame(1, cancellationToken: _actor.CancellationToken);
                    }

                    if (!string.IsNullOrEmpty(_cfg.AudioCast))
                    {
                        AudioManager.PlaySound(_cfg.AudioCast);
                    }

                    await UniTask.Delay((int)(atkInterval * 1000), cancellationToken: _actor.CancellationToken);
                }
            }
        }
    }
}