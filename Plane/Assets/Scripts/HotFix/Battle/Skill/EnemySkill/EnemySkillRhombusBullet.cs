using Cysharp.Threading.Tasks;
using GameCommon;
using System.Diagnostics;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// �����Ų���Ļ
    /// </summary>
    public class EnemySkillRhombusBullet : SkillBase
    {
        public EnemySkillRhombusBullet(int cfgID, ActorBase actor) : base(cfgID, actor)
        {
        }

        protected override void OnSkillStart()
        {
            _ = CreateBullet();
        }

        private async UniTask CreateBullet()
        {
            ActorEnemyBase enemy = Actor as ActorEnemyBase;
            if (enemy != null)
            {
                enemy.PlayAttackAnim();

                var bulletSpeed = GetAttr(EAttr.Speed);
                var num = GetAttrInt(EAttr.Num);
                var atkInterval = GetAttr(EAttr.AtkInterval);
                var startPos = Actor.WorldPos;
                var atkDir = (BattleManager.Instance.GetHeroPos() - startPos).normalized;
                var rightDir = new Vector3(-atkDir.y, atkDir.x, atkDir.z);
                var leftDir = new Vector3(atkDir.y, -atkDir.x, atkDir.z);

                float baseOffsetSpeed = 0.002f;

                for (int i = 0; i < num; i++)
                {
                    float speed = baseOffsetSpeed;
                    int index = i % 6;
                    if (index <= 3)
                    {
                        speed = speed * index;
                    }
                    else
                    {
                        speed = 3 * speed - (index - 3) * speed;
                    }
                    for (int j = 0; j < 2; j++) 
                    {
                        int dirIndex = j;
                        var offsetDir = (j == 0 ? rightDir : leftDir);
                        BattleObjectPool.Get(Cfg.Res, BattleRoot.Instance.ActorRoot, obj =>
                        {
                            var bullet = obj.GetComponent<EnemyBullet>();
                            if (bullet)
                            {
                                bullet.Show(startPos + 0.2f * offsetDir, atkDir * bulletSpeed + offsetDir * speed, this);
                            }
                            else
                            {
                                BattleObjectPool.Release(obj);
                            }
                        }, Cfg.Scale * 0.01f, false);
                        
                        await UniTask.DelayFrame(1, cancellationToken: _actor.CancellationToken);
                    }

                    if (!string.IsNullOrEmpty(_cfg.AudioCast))
                    {
                        AudioManager.PlaySound(_cfg.AudioCast);
                    }

                    await UniTask.Delay((int)(atkInterval * 1000), cancellationToken: _actor.CancellationToken);
                }
            }
        }
    }
}