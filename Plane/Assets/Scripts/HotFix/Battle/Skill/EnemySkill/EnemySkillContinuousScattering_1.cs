using Cysharp.Threading.Tasks;
using GameCommon;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices.ComTypes;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// �ڳ���ʱ���ڣ����ҷ������ָ���нǸ�����n���������ӵ��������·������Ա�ƫת
    /// </summary>
    public class EnemySkillContinuousScattering_1 : SkillBase
    {
        protected bool isSkillStart = false;
        protected float atkInterval;
        protected float timer;

        public EnemySkillContinuousScattering_1(int cfgID, ActorBase actor) : base(cfgID, actor)
        {
        }

        protected override void OnSkillStart()
        {
            base.OnSkillStart();

            atkInterval = GetAttr(EAttr.AtkInterval);
            isSkillStart = true;
            timer = atkInterval;

            ActorEnemyBase enemy = Actor as ActorEnemyBase;
            enemy?.PlayAttackAnim();
        }

        public override void OnUpdate()
        {
            if (isSkillStart)
            {
                timer += Time.deltaTime;
                if (timer >= atkInterval)
                {
                    _ = CreateBullet();
                    timer -= atkInterval;
                }
            }
            base.OnUpdate();
        }

        protected override void OnSkillEnd()
        {
            isSkillStart = false;
            timer = 0;
            base.OnSkillEnd();
        }

        private async UniTaskVoid CreateBullet()
        {
            ActorEnemyBase enemy = Actor as ActorEnemyBase;
            if (enemy != null)
            {
                Transform leftMuzzle = enemy.LeftMuzzle;
                Transform rightMuzzle = enemy.RightMuzzle;

                var bulletSpeed = GetAttr(EAttr.Speed);
                var num = GetAttrInt(EAttr.Num);
                var angle = Cfg.Angle;

                for (int i = 0; i < num; i++)
                {
                    for (int j = 0; j < 2; j++)
                    {
                        bool isLeft = (j % 2 == 0);
                        float rotateAngle = 0.5f * angle + i * angle;
                        Quaternion newRotation = Quaternion.AngleAxis(isLeft ? -rotateAngle : rotateAngle, Vector3.forward);
                        var speed = newRotation * Vector3.down * bulletSpeed;
                       
                        BattleObjectPool.Get(_cfg.Res, BattleRoot.Instance.ActorRoot, obj =>
                        {
                            var bullet = obj.GetComponent<EnemyBullet>();
                            if (bullet)
                            {
                                bullet.Show(isLeft ? leftMuzzle.position : rightMuzzle.position, speed, this);
                            }
                            else
                            {
                                BattleObjectPool.Release(obj);
                            }
                        }, _cfg.Scale * 0.01f, false);
                        
                        await UniTask.DelayFrame(1, cancellationToken: _actor.CancellationToken);
                    }

                    if (!string.IsNullOrEmpty(_cfg.AudioCast))
                    {
                        AudioManager.PlaySound(_cfg.AudioCast);
                    }
                }
            }
        }
    }
}