using Cysharp.Threading.Tasks;
using GameCommon;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices.ComTypes;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// 双炮口扫射（左右炮口间隔以n个子弹为1组发射间隔扫射弹幕，连续发射n组，每颗子弹间隔0.2，每组间隔间隔{攻击间隔}）
    /// </summary>
    public class EnemySkillDoubleStrafe_2 : SkillBase
    {
        public EnemySkillDoubleStrafe_2(int cfgID, ActorBase actor) : base(cfgID, actor)
        {
        }

        protected override void OnSkillStart()
        {
            base.OnSkillStart();

            _ = CreateBullet();
        }

        private async UniTask CreateBullet()
        {
            ActorEnemyBase enemy = Actor as ActorEnemyBase;
            if (enemy != null)
            {
                Transform leftMuzzle = enemy.LeftMuzzle;
                Transform rightMuzzle = enemy.RightMuzzle;

                enemy.PlayAttackAnim();

                var bulletSpeed = GetAttr(EAttr.Speed);
                var num = GetAttrInt(EAttr.Num);
                var atkInterval = GetAttr(EAttr.AtkInterval);
                var atkFrequency = GetAttrInt(EAttr.AtkFrequency);
                var angle = Cfg.Angle;
                var oneAngle = angle / num;

                Quaternion leftRotation = Quaternion.AngleAxis(-angle, Vector3.forward);
                Quaternion rightRotation = Quaternion.AngleAxis(angle, Vector3.forward);
                Vector3 leftStartDir = leftRotation * Vector3.down * bulletSpeed;
                Vector3 rightStartDir = rightRotation * Vector3.down * bulletSpeed;

                for (int i = 0; i < num * 2; i++)
                {
                    for (int j = 0; j < 2; j++)
                    {
                        bool isLeft = (j == 0);
                        Quaternion newRotation = Quaternion.AngleAxis(i * oneAngle * (isLeft ? 1 : -1), Vector3.forward);
                        Vector3 dir = newRotation * (isLeft ? leftStartDir : rightStartDir);

                        BattleObjectPool.Get(_cfg.Res, BattleRoot.Instance.ActorRoot, obj =>
                        {
                            var bullet = obj.GetComponent<EnemyBullet>();
                            if (bullet)
                            {
                                bullet.Show(isLeft ? leftMuzzle.position : rightMuzzle.position, dir, this);
                            }
                            else
                            {
                                BattleObjectPool.Release(obj);
                            }
                        }, _cfg.Scale * 0.01f, false);
                        
                        await UniTask.DelayFrame(1, cancellationToken: _actor.CancellationToken);
                    }

                    if (!string.IsNullOrEmpty(_cfg.AudioCast))
                    {
                        AudioManager.PlaySound(_cfg.AudioCast);
                    }

                    await UniTask.Delay((int)(atkInterval * 1000), cancellationToken: _actor.CancellationToken);
                }

                for (int i = 0; i < num + 1; i++)
                {
                    for (int j = 0; j < 2; j++)
                    {
                        bool isLeft = (j == 0);
                        Quaternion newRotation = Quaternion.AngleAxis(i * oneAngle * (isLeft ? -1 : 1), Vector3.forward);
                        Vector3 dir = newRotation * (isLeft ? rightStartDir : leftStartDir);

                        BattleObjectPool.Get(_cfg.Res, BattleRoot.Instance.ActorRoot, obj =>
                        {
                            var bullet = obj.GetComponent<EnemyBullet>();
                            if (bullet)
                            {
                                bullet.Show(isLeft ? leftMuzzle.position : rightMuzzle.position, dir, this);
                            }
                            else
                            {
                                BattleObjectPool.Release(obj);
                            }
                        }, _cfg.Scale * 0.01f, false);
                    }

                    if (!string.IsNullOrEmpty(_cfg.AudioCast))
                    {
                        AudioManager.PlaySound(_cfg.AudioCast);
                    }

                    await UniTask.Delay((int)(atkInterval * 1000), cancellationToken: _actor.CancellationToken);
                }
            }
        }
    }
}