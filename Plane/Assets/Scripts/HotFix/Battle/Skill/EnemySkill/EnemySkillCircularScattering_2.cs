using Cysharp.Threading.Tasks;
using GameCommon;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.InteropServices.ComTypes;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// 从怪物中心发射点发射，圆形发射n个子弹，支持攻击次数和攻击间隔
    /// </summary>
    public class EnemySkillCircularScattering_2 : SkillBase
    {
        public EnemySkillCircularScattering_2(int cfgID, ActorBase actor) : base(cfgID, actor)
        {
        }

        protected override void OnSkillStart()
        {
            base.OnSkillStart();

            _ = CreateBullet();
        }

        private async UniTask CreateBullet()
        {
            ActorEnemyBase enemy = Actor as ActorEnemyBase;
            if (enemy != null)
            {
                enemy.PlayAttackAnim();

                var bulletSpeed = GetAttr(EAttr.Speed);
                var num = GetAttrInt(EAttr.Num);
                var atkFrequency = GetAttrInt(EAttr.AtkFrequency);
                var atkInterval = GetAttr(EAttr.AtkInterval);

                var angel = 360f / num;
                var startPos = Actor.WorldPos;

                Vector3 rotationAxis = Vector3.forward;
                Quaternion rotation = Quaternion.AngleAxis(0.5f * angel, rotationAxis);
                Vector3 startDir = rotation * Vector3.up;

                for (int j = 0; j < atkFrequency; j++)
                {
                    for (int i = 0; i < num; i++)
                    {
                        Quaternion newRotation = Quaternion.AngleAxis(i * angel, rotationAxis);
                        var newSpeed = newRotation * startDir.normalized * bulletSpeed;

                        BattleObjectPool.Get(Cfg.Res, BattleRoot.Instance.ActorRoot, obj =>
                        {
                            var bullet = obj.GetComponent<EnemyBullet>();
                            if (bullet)
                            {
                                bullet.Show(startPos, newSpeed, this);
                            }
                            else
                            {
                                BattleObjectPool.Release(obj);
                            }
                        }, Cfg.Scale * 0.01f, false);
                        
                        await UniTask.DelayFrame(1, cancellationToken: _actor.CancellationToken);
                    }

                    if (!string.IsNullOrEmpty(_cfg.AudioCast))
                    {
                        AudioManager.PlaySound(_cfg.AudioCast);
                    }

                    await UniTask.Delay((int)(atkInterval * 1000), cancellationToken: _actor.CancellationToken);
                }
            }
        }
    }
}