using System.Collections;
using System.Collections.Generic;
using GameCommon;
using UnityEngine;

namespace Battle
{
    public class SkillCircle : SkillBase
    {
        public SkillCircle(int cfgID, ActorBase actor) : base(cfgID, actor)
        {
        }

        public override void Cast()
        {
            base.Cast();

            var num = _attr.GetAttr(EAttr.Num);
            var radius = GetAttr(EAttr.AtkDistance) / (_cfg.Scale * 0.01f);
            var angel = 360f / num;
            var offset = Random.Range(0, 360f);
            for (int i = 0; i < num; i++)
            {
                var newAngle = offset + angel * i;
                
                BattleObjectPool.Get(_cfg.Res, BattleRoot.Instance.ActorRoot, obj =>
                {
                    var bullet = obj.GetComponent<CircleBase>();
                    if (bullet)
                    {
                        bullet.Show(this, radius, newAngle);
                    }
                    else
                    {
                        BattleObjectPool.Release(obj);
                    }
                }, _cfg.Scale * 0.01f, false);
            }

            if (!string.IsNullOrEmpty(_cfg.AudioCast))
            {
                AudioManager.PlaySound(_cfg.AudioCast,0.1f);
            }
        }

       
    }
}


