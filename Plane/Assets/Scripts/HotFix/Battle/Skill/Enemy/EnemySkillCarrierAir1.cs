using Cysharp.Threading.Tasks;
using GameCommon;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// ���ػ�����1��ÿ15���Ի�ͷΪ���ķ���3��������ɢ�䵯Ļ����Ļ����Ϊ90�㣬ÿ�ӵ��н�15�㣬��
    /// </summary>
    public class EnemySkillCarrierAir1 : SkillBase
    {
        public EnemySkillCarrierAir1(int cfgID, ActorBase actor) : base(cfgID, actor)
        {
            _cdTime = 0;
        }

        protected override void OnSkillStart()
        {
            _ = CreateBullet();
        }

        private async UniTask CreateBullet()
        {
            var bulletSpeed = GetAttr(EAttr.Speed);
            var num = GetAttrInt(EAttr.Num);

            ActorEnemyBase actorEnemyBase = Actor as ActorEnemyBase;
            if (actorEnemyBase != null)
            {
                actorEnemyBase.PlayAttackAnim();
            }

            //��Z��
            Vector3 rotationAxis = Vector3.forward;
            Quaternion rotation = Quaternion.AngleAxis(-45, rotationAxis);
            Vector3 startDir = rotation * Vector3.down;

            for (int i = 0; i < num; i++)
            {
                for (int j = 0; j < 7; j++)
                {
                    Quaternion newRotation = Quaternion.AngleAxis(15 * j, rotationAxis);
                    var speed = newRotation * startDir.normalized * bulletSpeed;
                    BattleObjectPool.Get(_cfg.Res, BattleRoot.Instance.ActorRoot, obj =>
                    {
                        var bullet = obj.GetComponent<EnemyBullet>();
                        if (bullet)
                        {
                            bullet.Show(Actor.AttackPos, speed, this);
                        }
                        else
                        {
                            BattleObjectPool.Release(obj);
                        }
                    }, _cfg.Scale * 0.01f, false);
                    
                    await UniTask.DelayFrame(1, cancellationToken: _actor.CancellationToken);
                }

                if (!string.IsNullOrEmpty(_cfg.AudioCast))
                {
                    AudioManager.PlaySound(_cfg.AudioCast);
                }

                await UniTask.Delay(200, cancellationToken: _actor.CancellationToken);
            }
        }
    }
}