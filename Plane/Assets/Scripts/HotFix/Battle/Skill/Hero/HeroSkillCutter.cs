using Cysharp.Threading.Tasks;
using GameCommon;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// ����װ���и������ӷɻ�����������ĵз�Ŀ�귢��һ�����̣����к�͸Ŀ��������У���ײ����Ļ��Ե����е��䣬5�����ʧ������buff������ͬʱ����2�����̣��������2s��
    /// </summary>
    public class HeroSkillCutter : SkillBase
    {
        public HeroSkillCutter(int cfgID, ActorBase actor) : base(cfgID, actor)
        {
            _durationMax = 1;
        }

        protected override void OnSkillStart()
        {
            base.OnSkillStart();

            _ = CreateBullet();
        }

        private async UniTask CreateBullet()
        {
            var atkFrequency = GetAttrInt(EAttr.AtkFrequency);
            var atkInterval = GetAttr(EAttr.AtkInterval);
            for (var i = 0; i < atkFrequency; i++)
            {
                Vector3 startPos = Vector3.zero;
                if (BattleManager.LevelModel)
                {
                    startPos = BattleManager.Instance.Hero.WorldPos;
                }
                else
                {
                    startPos = GoldLevelManager.Instance.Hero.WorldPos;
                }

                var target = GetTarget();
                var targetPos = target ? target.WorldPos : startPos + Random.onUnitSphere;
                targetPos.z = 0;

                var atkSpeed = GetAttr(EAttr.Speed);
                var speed = (targetPos - startPos).normalized * atkSpeed;

                BattleObjectPool.Get(_cfg.Res, BattleRoot.Instance.ActorRoot, obj =>
                {
                    var bullet = obj.GetComponent<BulletBase>();
                    if (bullet)
                    {
                        bullet.Show(startPos, speed, this);
                    }
                    else
                    {
                        BattleObjectPool.Release(obj);
                    }
                }, _cfg.Scale * 0.01f, false);

                if (!string.IsNullOrEmpty(_cfg.AudioCast))
                {
                    AudioManager.PlaySound(_cfg.AudioCast);
                }

                await UniTask.Delay((int)(atkInterval * 1000), cancellationToken: _actor.CancellationToken);
            }


        }
    }
}