using Cysharp.Threading.Tasks;
using GameCommon;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// 主角装备激光发射器（锁定随机目标）
    /// </summary>
    public class HeroSkillPulse : SkillBase
    {
        protected List<BulletPulsePoint> bulletPulseList;
        public HeroSkillPulse(int cfgID, ActorBase actor) : base(cfg<PERSON>, actor)
        {
            bulletPulseList = new List<BulletPulsePoint>();
        }

        protected override void OnSkillStart()
        {
            base.OnSkillStart();

            CreateBullet();
        }

        protected override void OnSkillEnd()
        {
            if (bulletPulseList != null)
            {
                foreach (var laser in bulletPulseList)
                {
                    laser.Hide();
                }
                bulletPulseList.Clear();
            }

            base.OnSkillEnd();
        }

        private void CreateBullet()
        {
            for (int i = 0; i < 2; i++)
            {
                int index = i;
                BattleObjectPool.Get(_cfg.Res, BattleRoot.Instance.ActorRoot, obj =>
                {
                    var bulletPulse = obj.GetComponent<BulletPulsePoint>();
                    if (bulletPulse)
                    {
                        ActorHeroBase hero = Actor as ActorHeroBase;
                        if (hero != null)
                        {
                            bulletPulse.Show(hero.ComHeroWingman.GetWingmanPos(InsID, index), Vector3.up, this);
                        }
                        else
                        {
                            bulletPulse.Show(Actor.AttackPos, Vector3.up, this);
                        }
                        bulletPulseList.Add(bulletPulse);
                    }
                    else
                    {
                        BattleObjectPool.Release(obj);
                    }
                }, _cfg.Scale * 0.01f, false);
            }

            if (!string.IsNullOrEmpty(_cfg.AudioCast))
            {
                AudioManager.PlaySound(_cfg.AudioCast);
            }
        }
    }
}