using Cysharp.Threading.Tasks;
using GameCommon;
using System.Collections;
using System.Collections.Generic;
using Unity.VisualScripting;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// 毁灭之刃
    /// </summary>
    public class HeroSkillBezierBlade : SkillBase
    {
        private const int ShootAngle = 15;

        public HeroSkillBezierBlade(int cfgID, ActorBase actor) : base(cfgID, actor)
        {
        }

        protected override void OnSkillStart()
        {
            base.OnSkillStart();

            _ = CreateBullet();
        }

        private async UniTask CreateBullet()
        {
            var atkFrequency = GetAttrInt(EAttr.AtkFrequency);
            var atkInterval = GetAttr(EAttr.AtkInterval);
            var atkDistance = GetAttr(EAttr.AtkDistance);

            int atkPosIndex = 1;
            for (int j = 0; j < atkFrequency; j++)
            {
                var num = GetAttr(EAttr.Num);
                var bulletSpeed = GetAttr(EAttr.Speed);

                if (Actor.IsDestroyed())
                    return;
                for (var i = 0; i < num; i++)
                {
                    bool flip = i % 2 == 0;
                    var startPos = ((ActorHeroBase)Actor).AttackPoss[atkPosIndex++];
                    Quaternion rotation = Quaternion.AngleAxis(flip ? -ShootAngle : ShootAngle, Vector3.forward);
                    var startDir = rotation * Vector3.up;
                    var speed = startDir.normalized * bulletSpeed;
                    int bezierPosIndex = i / 2;

                    BattleObjectPool.Get(_cfg.Res, BattleRoot.Instance.ActorRoot, obj =>
                            OnBulletCreated(obj, flip, bezierPosIndex, startPos, startDir, atkDistance, speed).Forget(),
                        _cfg.Scale * 0.01f, false);

                    // if (i % 2 == 1)
                        await UniTask.Delay(80, cancellationToken: _actor.CancellationToken);
                }

                if (!string.IsNullOrEmpty(_cfg.AudioCast))
                    AudioManager.PlaySound(_cfg.AudioCast);

                await UniTask.Delay((int)(atkInterval * 1000), cancellationToken: _actor.CancellationToken);
            }
        }

        private async UniTask OnBulletCreated(GameObject obj,
            bool flip, int bezierPosIndex, Vector3 startPos, Vector3 startDir, float atkDistance, Vector3 speed)
        {
            await UniTask.WaitForEndOfFrame(GameManager.Instance, _actor.CancellationToken);
            var bullet = obj.GetComponent<BulletBezierBlade>();
            if (bullet)
            {
                bullet.Flip = flip;
                bullet.EndPos = startPos + startDir * atkDistance;
                bullet.BezierPosIndex = bezierPosIndex;
                bullet.Show(startPos, speed, this);
            }
            else
            {
                BattleObjectPool.Release(obj);
            }
        }
    }
}