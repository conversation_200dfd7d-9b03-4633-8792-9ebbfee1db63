using Cysharp.Threading.Tasks;
using GameCommon;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace Battle
{
    /// <summary>
    /// ����װ���������������Թ�����Χ�ڵĵз���λ�������ɴ�͸��λ����˺���
    /// </summary>
    public class HeroSkillFlamethrower : SkillBase
    {
        BulletFlame bulletFlame;
        public HeroSkillFlamethrower(int cfgID, ActorBase actor) : base(cfgID, actor)
        {
        }

        protected override void OnSkillStart()
        {
            base.OnSkillStart();

            CreateBullet();
        }

        protected override void OnSkillEnd()
        {
            base.OnSkillEnd();

            if (bulletFlame != null)
            {
                bulletFlame.Hide();
            }
        }

        private void CreateBullet()
        {
            BattleObjectPool.Get(_cfg.Res, BattleRoot.Instance.ActorRoot, obj =>
            {
                var bullet = obj.GetComponent<BulletFlame>();
                if (bullet)
                {
                    bulletFlame = bullet;
                    bullet.Show(Actor.WorldPos, Vector3.up, this);
                }
                else
                {
                    bulletFlame = null;
                    BattleObjectPool.Release(obj);
                }
            }, _cfg.Scale * 0.01f, false);

            if (!string.IsNullOrEmpty(_cfg.AudioCast))
            {
                AudioManager.PlaySound(_cfg.AudioCast);
            }
        }
    }
}