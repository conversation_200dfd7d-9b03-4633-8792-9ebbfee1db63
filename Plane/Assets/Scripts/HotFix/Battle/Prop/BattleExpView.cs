using Battle;
using UnityEngine;

public class BattleExpView : MonoBehaviour
{
    public SpriteRenderer spr;
    protected int _id;
    protected float _flyTime;
    protected float _movePercent;
    protected bool _isShow;
    protected const float FLY_DURATION = 0.3f;
    public Vector3 WorldPos => transform.position;
    public bool IsShow => _isShow;

    /// <summary>
    /// 显示经验
    /// </summary>
    /// <param name="id">经验道具id</param>
    /// <param name="pos">出现位置</param>
    public virtual void Show(int id, Vector3 pos)
    {
        _id = id;
        transform.position = pos;
        _flyTime = 0;
        _isShow = true;
        LoadSprite();
        gameObject.SetActive(true);
    }

    protected virtual void LoadSprite()
    {
        if (spr != null)
        {
            var cfg = PropConfig.Instance.Get(_id);
            ResourceManager.LoadSprite(spr, cfg.iconAtlas, cfg.iconUrl);
        }
    }

    /// <summary>
    /// 开始收集经验
    /// </summary>
    public void Collect()
    {
        AddExp();
    }

    private void LateUpdate()
    {
        OnUpdate();
    }

    protected virtual void OnUpdate()
    {
        if (_isShow && _flyTime > 0)
        {
            _flyTime -= Time.deltaTime;
            _movePercent = Mathf.Clamp01((FLY_DURATION - _flyTime) / FLY_DURATION);
            transform.position = Vector3.Lerp(transform.position, BattleManager.Instance.GetHeroPos(), _movePercent);
            if (_flyTime < 0)
            {
                AddExp();
            }
        }
    }

    protected virtual void AddExp()
    {
        BattleManager.Instance.AddProp(_id);
        Hide();
    }

    public virtual void Hide()
    {
        _isShow = false;
        BattleObjectPool.Release(gameObject);
    }
}
