using System.Collections.Generic;
using UnityEngine;
using GameCommon;
using System.IO;

public class rechargeCfg
{
    /// <summary>
    /// ID
    /// </summary>
    public int id;
    /// <summary>
    /// 商品入口类型;1：免广告
    /// </summary>
    public int entryType;
    /// <summary>
    /// 商品页签种类；1免广告
    /// </summary>
    public int goodType;
    /// <summary>
    /// 数量
    /// </summary>
    public int count;
    /// <summary>
    /// 折扣
    /// </summary>
    public float discount;
    /// <summary>
    /// [解锁方式，价格]；1：免费，2：金币，3：钻石，4：水晶，5：充值币
    /// </summary>
    public int getType;
    /// <summary>
    /// [解锁方式，价格]；1：视频，2：金币，3：钻石，4：水晶，5：充值币
    /// </summary>
    public float price;
    /// <summary>
    /// 消费点ID配置充值必须添加
    /// </summary>
    public int iapId;
    /// <summary>
    /// 
    /// </summary>
    public string des;
    /// <summary>
    /// 返还比
    /// </summary>
    public int Rebates;
}

public class rechargeConfig:Singleton<rechargeConfig>,IConfig
{
    private bool _isLoad;
    private int _tryCount;
    private List<rechargeCfg> _listConfigs = new List<rechargeCfg>();
    private Dictionary<int,rechargeCfg> _dicConfigs = new Dictionary<int,rechargeCfg>();

    public void Load()
    {
        if (_tryCount > 3)
        {
            return;
        }
        _tryCount++;
        ResourceManager.LoadConfig("cfg_recharge",Read);
    }
    public bool IsLoad()
    {
        return _isLoad;
    }

    public void Read(byte[] data)
    {
        if (null == data)
        {
            Load();
            return;
        }
        
        MemoryStream ms = new MemoryStream(data);
        BinaryReader br = new BinaryReader(ms);
        var col = br.ReadInt32();
        for (int i = 0; i < col; i++)
        {
            var item = new rechargeCfg();
            item.id = br.ReadInt32();
            item.entryType = br.ReadInt32();
            item.goodType = br.ReadInt32();
            item.count = br.ReadInt32();
            item.discount = br.ReadSingle();
            item.getType = br.ReadInt32();
            item.price = br.ReadSingle();
            item.iapId = br.ReadInt32();
            item.des = br.ReadString();
            item.Rebates = br.ReadInt32();
            _listConfigs.Add(item);
        }
            
        ms.Close();
        br.Close();

        for (int i = 0; i < _listConfigs.Count; i++)
        {
            var key = _listConfigs[i].id;
            if (_dicConfigs.ContainsKey(key))
            {
                Debug.LogError("recharge config has same key id: " + key);
            }
            else
            {
                _dicConfigs.Add(key,_listConfigs[i]);
            }
        }
        _isLoad = true;
    }

    public rechargeCfg Get(int key)
    {
        if (_dicConfigs.ContainsKey(key))
        {
            return _dicConfigs[key];
        }


        return null;
    }

    public List<rechargeCfg> GetList()
    {
        return _listConfigs;
    }
}
