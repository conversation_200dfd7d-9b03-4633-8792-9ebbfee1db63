using System.Collections.Generic;
using UnityEngine;
using GameCommon;
using System.IO;

public class MonthActivityCfg
{
    /// <summary>
    /// ID
    /// </summary>
    public int ID;
    /// <summary>
    /// 签到循环类型
    /// </summary>
    public int CycleType;
    /// <summary>
    /// 天数
    /// </summary>
    public int Day;
    /// <summary>
    /// 免费领取奖励内容
    /// </summary>
    public int[] Reward;
    /// <summary>
    /// 是否可双倍领取
    /// </summary>
    public int DoubleGet;
}

public class MonthActivityConfig:Singleton<MonthActivityConfig>,IConfig
{
    private bool _isLoad;
    private int _tryCount;
    private List<MonthActivityCfg> _listConfigs = new List<MonthActivityCfg>();
    private Dictionary<int,MonthActivityCfg> _dicConfigs = new Dictionary<int,MonthActivityCfg>();

    public void Load()
    {
        if (_tryCount > 3)
        {
            return;
        }
        _tryCount++;
        ResourceManager.LoadConfig("cfg_monthactivity",Read);
    }
    public bool IsLoad()
    {
        return _isLoad;
    }

    public void Read(byte[] data)
    {
        if (null == data)
        {
            Load();
            return;
        }
        
        MemoryStream ms = new MemoryStream(data);
        BinaryReader br = new BinaryReader(ms);
        var col = br.ReadInt32();
        for (int i = 0; i < col; i++)
        {
            var item = new MonthActivityCfg();
            item.ID = br.ReadInt32();
            item.CycleType = br.ReadInt32();
            item.Day = br.ReadInt32();
            var RewardLen = br.ReadInt32();
            item.Reward = new int[RewardLen];
            for (int j = 0; j < RewardLen; j++)
            {
                item.Reward[j] = br.ReadInt32();
            }
            item.DoubleGet = br.ReadInt32();
            _listConfigs.Add(item);
        }
            
        ms.Close();
        br.Close();

        for (int i = 0; i < _listConfigs.Count; i++)
        {
            var key = _listConfigs[i].ID;
            if (_dicConfigs.ContainsKey(key))
            {
                Debug.LogError("MonthActivity config has same key ID: " + key);
            }
            else
            {
                _dicConfigs.Add(key,_listConfigs[i]);
            }
        }
        _isLoad = true;
    }

    public MonthActivityCfg Get(int key)
    {
        if (_dicConfigs.ContainsKey(key))
        {
            return _dicConfigs[key];
        }


        return null;
    }

    public List<MonthActivityCfg> GetList()
    {
        return _listConfigs;
    }
}
