using System.Collections.Generic;
using UnityEngine;
using GameCommon;
using System.IO;

public class CollectionGroupCfg
{
    /// <summary>
    /// ID
    /// </summary>
    public int ID;
    /// <summary>
    /// 分组icon
    /// </summary>
    public string Icon;
    /// <summary>
    /// 分组Bg
    /// </summary>
    public string Bg;
    /// <summary>
    /// 分组名字
    /// </summary>
    public string Name;
    /// <summary>
    /// 分组描述
    /// </summary>
    public string Desc;
    /// <summary>
    /// 包含对象
    /// </summary>
    public int[] ObjectId;
    /// <summary>
    /// 激活条件
    /// </summary>
    public int[] Condition;
    /// <summary>
    /// 属性奖励
    /// </summary>
    public string AttributeReward;
}

public class CollectionGroupConfig:Singleton<CollectionGroupConfig>,IConfig
{
    private bool _isLoad;
    private int _tryCount;
    private List<CollectionGroupCfg> _listConfigs = new List<CollectionGroupCfg>();
    private Dictionary<int,CollectionGroupCfg> _dicConfigs = new Dictionary<int,CollectionGroupCfg>();

    public void Load()
    {
        if (_tryCount > 3)
        {
            return;
        }
        _tryCount++;
        ResourceManager.LoadConfig("cfg_collectiongroup",Read);
    }
    public bool IsLoad()
    {
        return _isLoad;
    }

    public void Read(byte[] data)
    {
        if (null == data)
        {
            Load();
            return;
        }
        
        MemoryStream ms = new MemoryStream(data);
        BinaryReader br = new BinaryReader(ms);
        var col = br.ReadInt32();
        for (int i = 0; i < col; i++)
        {
            var item = new CollectionGroupCfg();
            item.ID = br.ReadInt32();
            item.Icon = br.ReadString();
            item.Bg = br.ReadString();
            item.Name = br.ReadString();
            item.Desc = br.ReadString();
            var ObjectIdLen = br.ReadInt32();
            item.ObjectId = new int[ObjectIdLen];
            for (int j = 0; j < ObjectIdLen; j++)
            {
                item.ObjectId[j] = br.ReadInt32();
            }
            var ConditionLen = br.ReadInt32();
            item.Condition = new int[ConditionLen];
            for (int j = 0; j < ConditionLen; j++)
            {
                item.Condition[j] = br.ReadInt32();
            }
            item.AttributeReward = br.ReadString();
            _listConfigs.Add(item);
        }
            
        ms.Close();
        br.Close();

        for (int i = 0; i < _listConfigs.Count; i++)
        {
            var key = _listConfigs[i].ID;
            if (_dicConfigs.ContainsKey(key))
            {
                Debug.LogError("CollectionGroup config has same key ID: " + key);
            }
            else
            {
                _dicConfigs.Add(key,_listConfigs[i]);
            }
        }
        _isLoad = true;
    }

    public CollectionGroupCfg Get(int key)
    {
        if (_dicConfigs.ContainsKey(key))
        {
            return _dicConfigs[key];
        }


        return null;
    }

    public List<CollectionGroupCfg> GetList()
    {
        return _listConfigs;
    }
}
