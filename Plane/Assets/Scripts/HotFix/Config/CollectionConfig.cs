using System.Collections.Generic;
using UnityEngine;
using GameCommon;
using System.IO;

public class CollectionCfg
{
    /// <summary>
    /// ID
    /// </summary>
    public int ID;
    /// <summary>
    /// 图鉴对象
    /// </summary>
    public int Object;
    /// <summary>
    /// 类型
    /// </summary>
    public int Type;
    /// <summary>
    /// 道具奖励
    /// </summary>
    public int[] PropReward;
    /// <summary>
    /// 属性奖励
    /// </summary>
    public int[] AttributeReward;
    /// <summary>
    /// 品质
    /// </summary>
    public int Quality;
    /// <summary>
    /// 图鉴分组
    /// </summary>
    public int GroupId;
    /// <summary>
    /// 对象获取提示
    /// </summary>
    public string Tips;
}

public class CollectionConfig:Singleton<CollectionConfig>,IConfig
{
    private bool _isLoad;
    private int _tryCount;
    private List<CollectionCfg> _listConfigs = new List<CollectionCfg>();
    private Dictionary<int,CollectionCfg> _dicConfigs = new Dictionary<int,CollectionCfg>();

    public void Load()
    {
        if (_tryCount > 3)
        {
            return;
        }
        _tryCount++;
        ResourceManager.LoadConfig("cfg_collection",Read);
    }
    public bool IsLoad()
    {
        return _isLoad;
    }

    public void Read(byte[] data)
    {
        if (null == data)
        {
            Load();
            return;
        }
        
        MemoryStream ms = new MemoryStream(data);
        BinaryReader br = new BinaryReader(ms);
        var col = br.ReadInt32();
        for (int i = 0; i < col; i++)
        {
            var item = new CollectionCfg();
            item.ID = br.ReadInt32();
            item.Object = br.ReadInt32();
            item.Type = br.ReadInt32();
            var PropRewardLen = br.ReadInt32();
            item.PropReward = new int[PropRewardLen];
            for (int j = 0; j < PropRewardLen; j++)
            {
                item.PropReward[j] = br.ReadInt32();
            }
            var AttributeRewardLen = br.ReadInt32();
            item.AttributeReward = new int[AttributeRewardLen];
            for (int j = 0; j < AttributeRewardLen; j++)
            {
                item.AttributeReward[j] = br.ReadInt32();
            }
            item.Quality = br.ReadInt32();
            item.GroupId = br.ReadInt32();
            item.Tips = br.ReadString();
            _listConfigs.Add(item);
        }
            
        ms.Close();
        br.Close();

        for (int i = 0; i < _listConfigs.Count; i++)
        {
            var key = _listConfigs[i].ID;
            if (_dicConfigs.ContainsKey(key))
            {
                Debug.LogError("Collection config has same key ID: " + key);
            }
            else
            {
                _dicConfigs.Add(key,_listConfigs[i]);
            }
        }
        _isLoad = true;
    }

    public CollectionCfg Get(int key)
    {
        if (_dicConfigs.ContainsKey(key))
        {
            return _dicConfigs[key];
        }


        return null;
    }

    public List<CollectionCfg> GetList()
    {
        return _listConfigs;
    }
}
