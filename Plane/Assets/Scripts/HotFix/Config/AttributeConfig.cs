using System.Collections.Generic;
using UnityEngine;
using GameCommon;
using System.IO;

public class AttributeCfg
{
    /// <summary>
    /// 属性ID
    /// </summary>
    public int ID;
    /// <summary>
    /// 属性名称
    /// </summary>
    public string Name;
    /// <summary>
    /// 图标
    /// </summary>
    public string Icon;
    /// <summary>
    /// 除以数值
    /// </summary>
    public int Divide;
    /// <summary>
    /// 是否是整型
    /// </summary>
    public bool IsInt;
    /// <summary>
    /// 是否是百分比
    /// </summary>
    public bool IsPercent;
    /// <summary>
    /// 关联加成属性
    /// </summary>
    public int AssociationAttr;
    /// <summary>
    /// 最终加成属性（和加成属性乘算）
    /// </summary>
    public int FinalAddAttr;
    /// <summary>
    /// 是否对主角生效
    /// </summary>
    public bool IsHero;
    /// <summary>
    /// 是否受技能伤害系数加成
    /// </summary>
    public bool IsSkillAdd;
}

public class AttributeConfig:Singleton<AttributeConfig>,IConfig
{
    private bool _isLoad;
    private int _tryCount;
    private List<AttributeCfg> _listConfigs = new List<AttributeCfg>();
    private Dictionary<int,AttributeCfg> _dicConfigs = new Dictionary<int,AttributeCfg>();

    public void Load()
    {
        if (_tryCount > 3)
        {
            return;
        }
        _tryCount++;
        ResourceManager.LoadConfig("cfg_attribute",Read);
    }
    public bool IsLoad()
    {
        return _isLoad;
    }

    public void Read(byte[] data)
    {
        if (null == data)
        {
            Load();
            return;
        }
        
        MemoryStream ms = new MemoryStream(data);
        BinaryReader br = new BinaryReader(ms);
        var col = br.ReadInt32();
        for (int i = 0; i < col; i++)
        {
            var item = new AttributeCfg();
            item.ID = br.ReadInt32();
            item.Name = br.ReadString();
            item.Icon = br.ReadString();
            item.Divide = br.ReadInt32();
            item.IsInt = br.ReadBoolean();
            item.IsPercent = br.ReadBoolean();
            item.AssociationAttr = br.ReadInt32();
            item.FinalAddAttr = br.ReadInt32();
            item.IsHero = br.ReadBoolean();
            item.IsSkillAdd = br.ReadBoolean();
            _listConfigs.Add(item);
        }
            
        ms.Close();
        br.Close();

        for (int i = 0; i < _listConfigs.Count; i++)
        {
            var key = _listConfigs[i].ID;
            if (_dicConfigs.ContainsKey(key))
            {
                Debug.LogError("Attribute config has same key ID: " + key);
            }
            else
            {
                _dicConfigs.Add(key,_listConfigs[i]);
            }
        }
        _isLoad = true;
    }

    public AttributeCfg Get(int key)
    {
        if (_dicConfigs.ContainsKey(key))
        {
            return _dicConfigs[key];
        }


        return null;
    }

    public List<AttributeCfg> GetList()
    {
        return _listConfigs;
    }
}
