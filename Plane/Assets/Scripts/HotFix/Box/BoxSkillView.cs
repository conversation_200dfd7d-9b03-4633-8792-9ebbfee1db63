using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using Battle;
using Cysharp.Threading.Tasks;
using DG.Tweening;
using GameCommon;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

/// <summary>
/// 装备盒子选择道具
/// </summary>
public class BoxSkillView : MonoBeh<PERSON>our,IBeginDragHandler,IDragHandler,IEndDragHandler,IPointerDownHandler,IPointerUpHandler
{
   public Image grid;
   public Image imgIcon;
   public Image tmpImgIcon;
   public Image imgTips;
   public CanvasGroup canvasGroup;
   public GameObject goMask;
   public RectTransform rectRoot;
   public TextMeshProUGUI txtSkillInfo;
   public GameObject skillInfo;
   public GameObject mergeEffect;
   public GameObject levelUpEffect;
   public TextMeshProUGUI txtLevel;
   public GameObject levelObj;
   public GameObject goMain;
   /// <summary>
   /// 可以合并的提示图片
   /// </summary>
   [SerializeField]
   public Image _imgMerge;

   //public Image imgLevel;

   private List<Image> listGrid = new List<Image>();
   private List<int> listGridIndex = new List<int>();
   private GameObject _effFinger;

   private BoxSkillData _data;
   private bool _isEnable;
   private bool _inDrag;
   private Vector3 _dragOffset;
   private int _timerInt=-1;
   private Vector3 _startDragPos;
   private GameObject _effSenior;
   private ESkillType _skillType;
   private SkillCfg _cfg;
   private Sequence _seqMergeTips;
   private Vector3 _imgMergepos;

   public BoxSkillData Data => _data;
   public ESkillType SkillType => _skillType;
   public int InsID => _data?.Ins ?? 0;
   public bool InDrag => _inDrag;
   public List<Image> ValidGrid => listGrid;
   
   public List<int> TempGrids = new List<int>();
   
   
   


   /// <summary>
   /// 显示
   /// </summary>
   /// <param name="data">技能数据</param>
   public void Show(BoxSkillData data)
   {
      if (_effFinger != null)
      {
         EffectManager.Instance.FreeEffect(_effFinger);
         _effFinger = null;
      }
      _seqMergeTips?.Complete();
      _imgMerge.transform.localPosition = _imgMergepos;
      _imgMerge.enabled = false;
      if (null != _data && _data.Ins == data.Ins)
      {
         Refresh();
         return;
      }
      
      _data = data;
      _isEnable = true;

      _cfg = SkillConfig.Instance.Get(data.SkillID);
      _skillType = (ESkillType)_cfg.SkillType;
      if (_skillType == ESkillType.Grid)
      {
         imgIcon.enabled = false;
         levelObj.SetActiveOptimize(false);
      }
      else
      {
         imgIcon.enabled = true;
         ResourceManager.LoadSprite(imgIcon, "battle_icon", _cfg.equip_icon, true);
         ResourceManager.LoadSprite(tmpImgIcon, "battle_icon", _cfg.equip_icon, true);
         txtSkillInfo.text = Util.GetLocalize(_cfg.Name);
         txtLevel.text =$"{_cfg.Level}";
         levelObj.SetActiveOptimize(true);
      }

      imgTips.enabled = false;

      var isMain = false;
      var cfgEquip = EquipConfig.Instance.Get(_cfg.Group);
      if (cfgEquip != null)
      {
         isMain = cfgEquip.ImportantEquip > 0;
      }
      goMain.SetActive(isMain);

      //if (data.IsLucky)
      //{
      //   EffectManager.Instance.PlayEffect("eff_lucky",transform,Vector3.zero, 1f,scale:0.8f);
      //   data.IsLucky = false;
      //}

      for (int i = listGrid.Count - 1; i >= 0; i--)
      {
         Destroy(listGrid[i].gameObject);
      }
      listGrid.Clear();
      listGridIndex.Clear();
      
      var xMin = float.MaxValue;
      var xMax = float.MinValue;
      var yMin = float.MaxValue;
      var yMax = float.MinValue;
      var cfgShape = ShapeConfig.Instance.Get(_cfg.Shape);
      if (null == cfgShape)
      {
         Debug.LogError($"skill shape error, skill id:{_cfg.ID},shape id:{_cfg.Shape}");
         return;
      }
      
      if (_cfg.ID / 10000 == 3)//合成的高级技能
      {
         var window = UIManager.Instance.GetOpenedWindow(EWindowName.Box);
         
         EffectManager.Instance.GetEffect(cfgShape.Effect, obj =>
         {
            _effSenior = obj;
            Util.SetParent(_effSenior.transform,transform);
            Util.SetRenderOrder(_effSenior, window.CurOrder + 1);
         });
      }
      
      for (int i = 0; i < 3; i++)
      {
         for (int j = 0; j < 3; j++)
         {
            var index = i * 3 + j;
            if (index < cfgShape.Grid.Length && cfgShape.Grid[index] == 1)
            {
               var pos = new Vector3(j * 106, -i * 106, 0);
               
               var g = Util.Clone(grid, $"grid_{index}");
               g.transform.SetSiblingIndex(1);
               g.transform.localPosition = pos;
               //g.transform.localScale = Vector3.one * (_skillType == ESkillType.Grid ? 1f : 1.2f);
               //g.enabled = true;
               //g.color = new Color(1, 1, 1, _skillType == ESkillType.Grid ? 1 : 0);
               g.transform.GetChild(0).gameObject.SetActive(_skillType == ESkillType.Grid);
               listGrid.Add(g);
               listGridIndex.Add(index);
             
               xMin = Mathf.Min(pos.x, xMin);
               xMax = Mathf.Max(pos.x, xMax);
               yMin = Mathf.Min(pos.y, yMin);
               yMax = Mathf.Max(pos.y, yMax);
            }
         }
      }

      var center = new Vector3(-(xMax - xMin) * 0.5f, (yMax - yMin) * 0.5f, 0);
      for (int i = 0; i < listGrid.Count; i++)
      {
         listGrid[i].transform.localPosition += center;
      }
      
      xMin -= 48;
      xMax += 48;
      yMin -= 48;
      yMax += 48;
      rectRoot.sizeDelta = new Vector2(xMax - xMin, yMax - yMin);

      levelObj.transform.localPosition = listGrid[^1].transform.localPosition + new Vector3(20, -20, 0);
      
      levelUpEffect.SetActive(false);
      goMask.SetActive(_data.AdID > 0 && !PlayerData.Instance.IsNoAds);
      gameObject.SetActive(true);
   }

   /// <summary>
   /// 刷新
   /// </summary>
   private void Refresh()
   {
      goMask.SetActive(_data.AdID > 0 && !PlayerData.Instance.IsNoAds);
      imgTips.enabled = false;
   }


   /// <summary>
   /// 设置是否可操作
   /// </summary>
   /// <param name="isEnable"></param>
   public void SetEnable(bool isEnable)
   {
      _isEnable = isEnable;
      canvasGroup.alpha = isEnable ? 1f : 0.5f;
      //imgIcon.color = new Color(1, 1, 1, isEnable ? 1f : 0.5f);
   }

   /// <summary>
   /// 设置挂点
   /// </summary>
   /// <param name="parent"></param>
   public void SetParent(RectTransform parent)
   {
      transform.SetParent(parent);
   }

   /// <summary>
   /// 设置超武合成提示状态
   /// </summary>
   public async Task SetMergeState(bool isTop)
   {
      if (_data.State != 1)
         _imgMerge.enabled = true;
      if (isTop)
      {
         var cfgSkill = SkillConfig.Instance.Get(_data.SkillID);
         var cfgShape = ShapeConfig.Instance.Get(cfgSkill.Shape);
         if (!string.IsNullOrEmpty(cfgShape.Tips))
         {
            ResourceManager.LoadSprite(imgTips,"box",cfgShape.Tips,true);
            imgTips.enabled = true;
         }
         else
         {
            imgTips.enabled = false;
         }
      }
      else
      {
         rectRoot.localScale = Vector3.one;

         if (_seqMergeTips != null)
         {
            _seqMergeTips.Kill(true);
            _seqMergeTips = null;
         }
         // await UniTask.DelayFrame(1);
         // _imgMergeNode.SetParent(transform);
         // await UniTask.DelayFrame(1);
         _imgMergepos = _imgMerge.transform.localPosition;
         _seqMergeTips = DOTween.Sequence();
         _seqMergeTips.Append(rectRoot.DOScale(0.9f, 0.5f));
         _seqMergeTips.Join(_imgMerge.transform.DOLocalMoveY(_imgMergepos.y - 10f, 0.5f));
         _seqMergeTips.Append(rectRoot.DOScale(1f, 0.5f));
         _seqMergeTips.Join(_imgMerge.transform.DOLocalMoveY(_imgMergepos.y, 0.5f).SetEase(Ease.OutSine));
         // _seqMergeTips.AppendInterval(2f);
         _seqMergeTips.SetLoops(-1);
         _seqMergeTips.Play();
      }
   }

   /// <summary>
   /// 设置位置
   /// </summary>
   /// <param name="localPos">位置</param>
   /// <param name="scale">大小</param>
   /// <param name="anim">是否动画</param>
   public void SetPos(Vector3 localPos, float scale, bool anim)
   {
      if (_seqMergeTips != null)
      {
         _seqMergeTips.Kill();
         _seqMergeTips = null;
      }
      
      if (anim)
      {
         transform.DOLocalMove(localPos, 0.2f);
         transform.DOScale(scale, 0.2f);
      }
      else
      {
         transform.localPosition = localPos;
         transform.localScale = Vector3.one * scale;
      }
   }

   private void SetLevelObjState(bool isShow)
   {
      levelObj.gameObject.SetActiveOptimize(isShow);
   }


   /// <summary>
   /// 隐藏
   /// </summary>
   public void Hide()
   {
      for (int i = listGrid.Count - 1; i >= 0; i--)
      {
         Destroy(listGrid[i].gameObject);
      }
      listGrid.Clear();
      listGridIndex.Clear();

      if (_effSenior)
      {
         EffectManager.Instance.FreeEffect(_effSenior);
         _effSenior = null;
      }
      
      if (_seqMergeTips != null)
      {
         _seqMergeTips.Kill();
         _seqMergeTips = null;
      }
      
      _data = null;
      gameObject.SetActive(false);
   }

   private bool InShape(Vector3 pos)
   {
      var inShape = false;
      for (int i = 0; i < listGrid.Count; i++)
      {
         var localPos = listGrid[i].rectTransform.InverseTransformPoint(pos);
         var contains = listGrid[i].rectTransform.rect.Contains(localPos);
         if (contains)
         {
            inShape = true;
            break;
         }
      }

      return inShape;
   }

   /// <summary>
   /// 开始拖拽
   /// </summary>
   /// <param name="eventData"></param>
   public void OnBeginDrag(PointerEventData eventData)
   {
      if (!_isEnable)
      {
         return;
      }
      
      if (_effFinger != null)
      {
         EffectManager.Instance.FreeEffect(_effFinger);
         _effFinger = null;
      }

      // if (_data.AdID > 0 && !PlayerData.Instance.IsNoAds)
      // {
      //    return;
      // }
      
      var pos = UIManager.Instance.UICamera.ScreenToWorldPoint(eventData.position);

      if (InShape(pos))
      {
         _dragOffset = transform.position - pos;
         _dragOffset.x = 0;
         _dragOffset.y = 0;
      
         _inDrag = true;
         transform.DOKill();
         transform.DOScale(1f, 0.2f);
         transform.transform.position = _dragOffset + pos;
         SetLevelObjState(false);
         GlobalEvent.DispatchEvent(EGameEvent.BoxDragBegin,this);
         
         AudioManager.PlaySound(EAudio.Button);
      }
      else
      {
         PassEvent(eventData, ExecuteEvents.beginDragHandler);
      }
   }

   /// <summary>
   /// 拖拽
   /// </summary>
   /// <param name="eventData"></param>
   public void OnDrag(PointerEventData eventData)
   {
      if (!_isEnable || !_inDrag)
      {
         PassEvent(eventData, ExecuteEvents.dragHandler);
         return;
      }
      
      var pos = UIManager.Instance.UICamera.ScreenToWorldPoint(eventData.position);
      transform.position = pos + _dragOffset;
      GlobalEvent.DispatchEvent(EGameEvent.BoxDrag,this);
   }

   /// <summary>
   /// 结束拖拽
   /// </summary>
   /// <param name="eventData"></param>
   public void OnEndDrag(PointerEventData eventData)
   {
      if (!_isEnable || !_inDrag)
      {
         PassEvent(eventData, ExecuteEvents.endDragHandler);
         return;
      }
      _inDrag = false;
     
      AudioManager.PlaySound(EAudio.Set);

      if (_data.AdID > 0)
      {
         // var tipArgs = new TipsWindowArgs()
         // {
         //    adId = Data.AdID,
         //    title = Util.GetLocalize("assemble_title_7"),
         //    tips = Util.GetLocalize("assemble_des_6"),
         //    btnName = Util.GetLocalize("Shop_BtnText_Free"),
         //    argId = Data.SkillID,
         //    count = 1,
         //    funcType = TipsWindowFunc.AdGetEquip,
         //    _adCallBack = () =>
         //    {
         //       BattleData.Instance.UnlockSkillByAd(InsID,false);
         //       RealEndDrag();
         //    },
         //    closeCallBack = () =>
         //    {
         //       GlobalEvent.DispatchEvent(EGameEvent.BoxUpdate);
         //    }
         // };
         //        
         // UIManager.Instance.OpenPop(EWindowName.CommonTipsWindow,tipArgs);
            var win = UIManager.Instance.GetOpenedWindow(EWindowName.Box);
            var boxWin = win != null ? win as BoxWindow : null;
            if (boxWin != null && boxWin.PlaceFlag)
                ADManager.Instance.ShowRewardAd(
                    _data.AdID,
                    isSuccess =>
                    {
                        if (isSuccess)
                        {
                            BattleData.Instance.UnlockSkillByAd(InsID, false);
                        }
                        RealEndDrag();
                    }
                );
            else
                RealEndDrag();
      }
      else
      {
         RealEndDrag();
      }
   }

   private void RealEndDrag()
   {
      var cfgSkill = SkillConfig.Instance.Get(_data.SkillID);
      _skillType = (ESkillType)cfgSkill.SkillType;
      SetLevelObjState(_skillType != ESkillType.Grid);
      GlobalEvent.DispatchEvent(EGameEvent.BoxDragEnd,this);
   }

   /// <summary>
   /// 检查格子是否被技能覆盖
   /// </summary>
   /// <param name="pos"></param>
   /// <param name="distance"></param>
   /// <returns></returns>
   public int CheckCover(Vector3 pos,float distance)
   {
      var localPos = transform.InverseTransformPoint(pos);
      
      var touchIndex = -1;
      for (int i = 0; i < listGrid.Count; i++)
      {
         if (Vector3.Distance(localPos,listGrid[i].transform.localPosition) < distance)
         {
            touchIndex = listGridIndex[i];
            break;
         }
      }
      return touchIndex;
   }

   public void OnPointerClick(PointerEventData eventData)
   {
      //GlobalEvent.DispatchEvent(EGameEvent.BoxClickSkill,this);
   }

   public void SetSkillInfoActive(bool isShow)
   {
      skillInfo.SetActiveOptimize(isShow);
      if (_timerInt!=-1)
      {
         DelayTimer.Remove(_timerInt);
      }
      DelayTimer.Add(1, true, () =>
      {
         skillInfo.SetActiveOptimize(!isShow);
      });
   }

   public void OnPointerDown(PointerEventData eventData)
   {
      var pos = UIManager.Instance.UICamera.ScreenToWorldPoint(eventData.position);
      if (InShape(pos))
      {
         _startDragPos = eventData.position;
      }
      else
      {
         PassEvent(eventData, ExecuteEvents.pointerDownHandler);
      }
      
   }

   public void OnPointerUp(PointerEventData eventData)
   {
      if (_isEnable && Vector3.Distance(eventData.position, _startDragPos) < 5f) 
      {
         var pos = UIManager.Instance.UICamera.ScreenToWorldPoint(eventData.position);
         if (InShape(pos))
         {
            GlobalEvent.DispatchEvent(EGameEvent.BoxClickSkill,this);
         }
         else
         {
            PassEvent(eventData, ExecuteEvents.pointerUpHandler);
         }
      }
   }
   
   private void PassEvent<T>(PointerEventData data,ExecuteEvents.EventFunction<T> function)
      where T : IEventSystemHandler
   {
      List<RaycastResult> results = new List<RaycastResult>();
      EventSystem.current.RaycastAll(data, results); 
      GameObject current = data.pointerCurrentRaycast.gameObject ;
      for(int i =0; i< results.Count;i++)
      {
         if(current != results[i].gameObject)
         {
            ExecuteEvents.Execute(results[i].gameObject, data,function);
            break;
         }
      }
   }

   public void ShowUpEffect()
   {
      levelUpEffect.SetActive(true);

      StartCoroutine(DelayHide(1f));
   }

   /// <summary>
   /// 显示手指提示动画
   /// </summary>
   public void ShowFinger()
   {
      if (_effFinger)
      {
         return;
      }
      EffectManager.Instance.GetEffect("eff_finger",eff =>
      {
         _effFinger = eff;
         _effFinger.gameObject.SetActive(true);
         Util.SetParent(_effFinger.transform, transform);
      });
   }

   private IEnumerator DelayHide(float delay)
   {
      yield return new WaitForSeconds(1f);
      levelUpEffect.SetActive(false);
   }


   private void OnDisable()
   {
      if (_seqMergeTips != null)
      {
         _seqMergeTips.Kill();
         _seqMergeTips = null;
      }
   }
}
