using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using GameCommon;
using TMPro;
using Unity.VisualScripting;
using UnityEngine;

/// <summary>
/// 战机数据
/// </summary>
public class RaisingCharData : DataBase<RaisingCharData>
{
    /// <summary>
    /// 配置
    /// </summary>
    public HeroConfig config => HeroConfig.Instance;
    public List<HeroCfg> ConfigList => _configList;
    List<HeroCfg> _configList;
    RaisingCharLevelData levelData => RaisingCharLevelData.Instance;

    /// <summary>
    /// 剩余次数
    /// </summary>
    public int ADLevelUpNumberRemain => TimesData.GetTimesByAdId(LevelUpADId);
    Dictionary<int, int> heroData;

    /// <summary>
    /// 广告可以升级的次数
    /// </summary>
    public int ADLevelUpMaxNumber { get; private set; }

    /// <summary>
    /// 广告可以解锁的次数
    /// </summary>
    public int ADLevelUnlockpMaxNumber { get; private set; }

    /// <summary>
    /// 剩余看广告解锁的次数
    /// </summary>
    public int ADUnlockNumberRemain => TimesData.GetTimesByAdId(LevelUnlockADId);

    /// <summary>
    /// 升级广告
    /// </summary>
    int LevelUpADId;

    /// <summary>
    /// 解锁广告
    /// </summary>
    int LevelUnlockADId;
    string KEY_HeroData = "KEY_HeroData";

    /// <summary>
    /// 看广告升级的次数
    /// </summary>
    string KEY_ADUpCount = "KEY_ADUpCount";
    string KEY_ADUnlockCount = "KEY_ADUnlockCount";

    /// <summary>
    /// 角色数据
    /// Key: id
    /// Value: 等级
    /// </summary>
    SerializeDictionary<int, int> SerializeHeroData;
    SerializeDictionary<int, int> ADUpCount;
    SerializeInt ADunlockCount;

    // const int MaxLevel = ;

    public override void Init()
    {
        base.Init();
        LevelUpADId = ConstValue.LevelUpADId;
        LevelUnlockADId = ConstValue.aircraft_102_unlock;
        ADLevelUpMaxNumber = TimesData.GetMaxTimesByAdId(LevelUpADId);
        ADLevelUnlockpMaxNumber = TimesData.GetMaxTimesByAdId(LevelUnlockADId);
        SerializeHeroData = new SerializeDictionary<int, int>(KEY_HeroData);
        ADUpCount = new SerializeDictionary<int, int>(
            KEY_ADUpCount,
            new Dictionary<int, int>() { { DateTime.Now.DayOfYear, 0 } }
        );
        ADunlockCount = new SerializeInt(KEY_ADUnlockCount, 0);
        var list = HeroConfig.Instance.GetList();
        list.Sort(
            (a, b) =>
            {
                return a.ID - b.ID;
            }
        );
        _configList = list;
        foreach (var cfg in _configList)
        {
            if (!SerializeHeroData.ContainsKey(cfg.ID))
            {
                if (cfg.UnlockId > 0) // 有条件的先锁定
                    SerializeHeroData.SetValue(cfg.ID, -1); // -1 没解锁
                else
                    SerializeHeroData.SetValue(cfg.ID, 0);
            }
        }
        if (!SerializeHeroData.ContainsKey(1001))
        {
            SerializeHeroData.SetValue(1001, 0);
        }
        heroData = SerializeHeroData.Get();
        UnlockConditionsPlane();
        GlobalEvent.AddEvent(EGameEvent.DayChange, OnDayChange);
    }

    private void OnDayChange(object[] args)
    {
        ADUpCount.Clear();
        ADUpCount.Set(new Dictionary<int, int>() { { DateTime.Now.DayOfYear, 0 } });
    }

    public int GetHeroLevel(int heroId)
    {
        heroId = 1001;
        return heroData[heroId];
    }

    /// <summary>
    /// 是否已经解锁
    /// </summary>
    /// <param name="heroId"></param>
    /// <returns></returns>
    public bool HeroIsUnlock(int heroId)
    {
        if (heroData.ContainsKey(heroId))
        {
            var unLock = heroData[heroId] > -1;
            return unLock;
        }

        return false;
    }

    public void UnlockConditionsPlane() // 102飞机以前解锁有问题手动判断下
    {
        var list = HeroConfig.Instance.GetList();
        foreach (var cfg in list)
        {
            var heroId = cfg.ID;
            if (!HeroIsUnlock(heroId) && !UnLockData.Instance.IsUnLock(config.Get(heroId).UnlockId))
            {
                if (heroId == 102 && ADUnlockNumberRemain <= 0)
                {
                    heroData[heroId] = 0;
                }
            }
        }

        SerializeHeroData.Set(heroData);
    }

    public void CheckAutoUnlock()
    {
        foreach (var cfg in HeroConfig.Instance.GetList())
        {
            var heroId = cfg.ID;
            if (!HeroIsUnlock(heroId) && UnLockData.Instance.IsUnLock(config.Get(heroId).UnlockId))
            {
                UnlockHero(heroId);
            }
        }
    }

    public HeroTalentCfg GetConfig(int heroId)
    {
        var data = levelData.GetConfigByLevel(GetHeroLevel(heroId));
        return data;
    }

    /// <summary>
    /// 获得升级消耗
    /// </summary>
    /// <param name="targetLevel"></param>
    /// <returns></returns>
    public List<RewardShowItem> GetLevelCost(int heroId)
    {
        var tmp = levelData.GetLevelCost(GetHeroLevel(heroId) + 1);
        return tmp;
    }

    Dictionary<int, int> attrTmpDic;

    /// <summary>
    /// 获得等级的总加成
    /// </summary>
    /// <param name="targetLevel"></param>
    /// <returns></returns>
    public Dictionary<int, int> GetLevelAllAttr(int heroId, bool show = false)
    {
        attrTmpDic = new(levelData.GetLevelAllAttr(GetHeroLevel(heroId)));
        Dictionary<int, int> _attrTmpDic;
        if (attrTmpDic.Count < 2 && show) // 不够两个补充两个去显示
        {
            _attrTmpDic = new(levelData.GetLevelAllAttr(99));
            foreach (var item in _attrTmpDic)
            {
                if (!attrTmpDic.ContainsKey(item.Key))
                    attrTmpDic.Add(item.Key, 0);
            }
        }
        return attrTmpDic;
    }

    /// <summary>
    /// 获得格式化字符
    /// </summary>
    /// <param name="targetLevel"></param>
    /// <returns></returns>
    public string AttrValueFormat(int attrType, int attrValue)
    {
        return levelData.AttrValueFormat(attrType, attrValue);
    }

    /// <summary>
    /// 解锁战机
    /// </summary>
    /// <param name="attrType"></param>
    /// <param name="attrValue"></param>
    /// <returns></returns>
    public void UnlockHero(int heroId, bool force = false)
    {
        if (!HeroIsUnlock(heroId) &&
            (force || UnLockData.Instance.IsUnLock(config.Get(heroId).UnlockId)))
        {
            heroData[heroId] = 0;
            SerializeHeroData.Set(heroData);

            Debug.LogFormat("Unlock hero: {0}", heroId);
            GlobalEvent.DispatchEvent(EGameEvent.HeroUnlocked, heroId);
        }
    }

    /// <summary>
    /// 获得解锁类型
    /// </summary>
    /// <param name="heroId"></param>
    /// <returns></returns>
    public FunctionUnlockType GetUnlockType(int heroId, out int arg)
    {
        int unlockId = config.Get(heroId).UnlockId;
        return UnLockData.Instance.GetCurrentUnlockType(unlockId, out arg);
    }

    /// <summary>
    /// 获得解锁条件的值
    /// </summary>
    /// <param name="heroId"></param>
    /// <returns></returns>
    public int GetUnlockValue(int heroId)
    {
        int level = UnLockConfig.Instance.Get(config.Get(heroId).UnlockId).UnlockLevel[0];
        return level;
    }

    public bool LevelIsMax(int heroId)
    {
        heroId = 1001;
        return heroData[heroId] >= HeroTalentConfig.Instance.GetList().Count;
    }

    /// <summary>
    /// 等级提升
    /// </summary>
    /// <param name="attrType"></param>
    /// <param name="attrValue"></param>
    /// <returns></returns>
    public void HeroLevelUp(int heroId)
    {
        heroId = 1001;
        if (heroData.ContainsKey(heroId) && (!LevelIsMax(heroId)))
            SetHeroDic(heroId, heroData[heroId] + 1);
    }

    /// <summary>
    /// 设置战机数据
    /// </summary>
    /// <param name="heroId"></param>
    /// <param name="level"></param>
    private void SetHeroDic(int heroId, int level)
    {
        heroId = 1001;
        if (!heroData.TryAdd(heroId, level))
            heroData[heroId] = level;
        SerializeHeroData.Set(heroData);
    }

    /// <summary>
    /// 展示升级的广告
    /// </summary>
    /// <param name="call"></param>
    public void ShowLevelAD(int heroId, Action<bool> call)
    {
        if (ADLevelUpNumberRemain > 0)
            ADManager.Instance.ShowRewardAd(
                LevelUpADId,
                (v) =>
                {
                    HeroLevelUp(heroId);
                    int value = ADUpCount.GetValue(DateTime.Now.DayOfYear, 0) + 1;
                    ADUpCount.SetValue(DateTime.Now.DayOfYear, value);
                    call?.Invoke(v);
                }
            );
        else
        {
            var cfgProp = PropConfig.Instance.Get((int)ECoinType.Gold);
            Util.FloatTips(Util.GetLocalize("Common_Tip_UseUp"));
            call?.Invoke(false);
        }
    }

    /// <summary>
    /// 展示解锁的广告
    /// </summary>
    /// <param name="call"></param>
    public void ShowUnlockAD(int heroId, Action<bool> call)
    {
        int adId = heroId == 102
            ? LevelUnlockADId
            : UnLockData.Instance.GetUnlockArg(HeroConfig.Instance.Get(heroId).UnlockId, FunctionUnlockType.AdId);
        int remain = heroId == 102 ? ADUnlockNumberRemain : TimesData.GetTimesByAdId(adId);
        if (remain > 0)
            ADManager.Instance.ShowRewardAd(
                adId,
                (v) =>
                {
                    if (heroId == 102)
                    {
                        int value = ADunlockCount.Get() + 1;
                        ADunlockCount.Set(value);
                        if (ADUnlockNumberRemain <= 0) // 看完了所有次数才解锁
                            UnlockHero(heroId, true);
                    }
                    else if (TimesData.GetTimesByAdId(adId) <= 0)
                        UnlockHero(heroId, true);

                    call?.Invoke(v);
                }
            );
    }

    /// <summary>
    /// 获得对应类型的加成
    /// </summary>
    /// <param name="call"></param>
    public Dictionary<int, int> GetRaisingCharAttr(int heroId, int equipType)
    {
        Dictionary<int, int> tmp = new();
        var tmp1 = attrTmpDic = levelData.GetLevelAllAttrByType(GetHeroLevel(heroId), equipType);
        tmp.AddRange(tmp1);
        return tmp1;
    }
}
