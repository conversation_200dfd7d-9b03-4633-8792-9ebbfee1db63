using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Battle;
using GameCommon;
using NUnit.Framework.Interfaces;
using UnityEngine;

public class SingleEquipData
{
    public int Level;

    public SingleEquipData()
    {
        Level = 1;
    }

    public SingleEquipData Rise()
    {
        Level++;
        return this;
    }
}

public class EquipData : DataBase<EquipData>
{
    private const string Key_HoleData = "EquipData.HoleData";
    private const string Key_EquipData = "EquipData.EquipData";

    // 槽位数据
    private SerializeDictionary<int, int> _holeData; // key:槽位id: 1-8,value:-1:未解锁,0:空,xxx:装备id

    // 装备数据
    private SerializeDictionary<int, SingleEquipData> _equipData;
    private SerializeDictionary<int, int> _dayOfLevel;
    private string Key_DayOfLevelUp = "Key_DayOfLevelUp";
    private List<int> _unlockList; // 已解锁的
    private List<int> _lockList; // 未解锁的

    private List<int> _newUnlockList; //新解锁的

    public Dictionary<int, int> HoleData => _holeData.Get();
    public List<int> UnlockList => _unlockList;
    public List<int> LockList => _lockList;
    public List<int> NewUnlockList => _newUnlockList;

    private readonly Dictionary<int, int> _costEquipMap = new();

    public override void Init()
    {
        base.Init();

        _holeData = new SerializeDictionary<int, int>(Key_HoleData);
        _equipData = new SerializeDictionary<int, SingleEquipData>(Key_EquipData);

        InitHoleData();
        InitEquipData();
        CheckHoleData();
        UpdateBagList();

        AddListener(EGameEvent.LevelUpdate, OnLevelUpdate);
        AddListener(EGameEvent.UseHeroChange, OnLevelUpdate);
    }

    // 初始化槽位数据(全局只执行一次)
    private void InitHoleData()
    {
        // if (_holeData.Count > 0)
        //     return;

        // 生成新的空的槽位数据
        var maxHole = EquipConfig.Instance.GetList().Count; // ConstValue.EquipedMaxNum;
        for (int i = 1; i <= maxHole; i++)
        {
            // var unlockId = 100 + i;
            // Enum.TryParse<FunctionType>(unlockId.ToString(), out FunctionType functionType);
            // var isUnlock = UnLockData.Instance.IsUnLock(functionType);
            // var holeState = isUnlock ? 0 : -1;
            var holeState = 0;
            _holeData.SetValue(i, holeState);
        }
        // 装备默认武器(所装备武器和装备数量必须符合初始条件)
        string[] _defaultEquipArr = ConstValue.DefaultEquip.Split(',');
        int[] defaultEquipArr = new int[_defaultEquipArr.Length];
        for (int i = 0; i < _defaultEquipArr.Length; i++)
        {
            var equipId = _defaultEquipArr[i];
            var cfg = EquipConfig.Instance.Get(int.Parse(equipId));
            if (cfg != null && cfg.ImportantEquip == 1)
                if (BattleData.Instance.GetUseHreoMainEquipID() != int.Parse(equipId))
                {
                    defaultEquipArr[i] = BattleData.Instance.GetUseHreoMainEquipID();
                }
                else
                {
                    defaultEquipArr[i] = int.Parse(equipId);
                }
        }
        for (var i = 0; i < HoleData.ToList().Count; i++)
        {
            if (i < defaultEquipArr.Length)
            {
                _holeData.SetValue(i + 1, defaultEquipArr[i]);
            }
        }
    }

    // 策划需求:如果解锁数值调整,老玩家已解锁的槽位在新版本中不适用(装备同理)
    private void CheckHoleData()
    {
        // var keys = _holeData.Get().Keys.ToList();
        // foreach (var key in keys)
        // {
        //     var unlockId = 100 + key;
        //     var functionType = Enum.Parse<FunctionType>(unlockId.ToString());
        //     var isUnlock = UnLockData.Instance.IsUnLock(functionType);
        //     if (!isUnlock) // 未解锁
        //     {
        //         _holeData.SetValue(key, -1);
        //     }
        //     else if (_holeData.GetValue(key) == -1)
        //     {
        //         _holeData.SetValue(key, 0);
        //     }
        // }
    }

    // 初始化装备数据
    private void InitEquipData()
    {
        foreach (var equipCfg in EquipConfig.Instance.GetList())
        {
            if (equipCfg.Deploy > 0 && !_equipData.ContainsKey(equipCfg.ID))
            {
                _equipData.SetValue(equipCfg.ID, new SingleEquipData());
            }
        }

        foreach (var upgradeCfg in EquipUpgradeConfig.Instance.GetList())
        {
            if (upgradeCfg.UpgradeCostType.Length > 0)
                _costEquipMap[upgradeCfg.UpgradeCostType[0]] = upgradeCfg.EquipID;
        }
    }

    // 更新背包数据: 已解锁 && 未装备
    private void UpdateBagList()
    {
        var oldLockList = _lockList;
        _unlockList = new List<int>();
        _lockList = new List<int>();
        _newUnlockList = new List<int>();

        var equippedList = _holeData.Get().Values.ToList();
        foreach (var singleEquipData in _equipData.Get())
        {
            if (IsUnlock(singleEquipData.Key))
            {
                if (!equippedList.Contains(singleEquipData.Key))
                {
                    _unlockList.Add(singleEquipData.Key);
                }

                if (oldLockList != null && oldLockList.Contains(singleEquipData.Key))
                {
                    _newUnlockList.Add(singleEquipData.Key);
                }
            }
            else
            {
                _lockList.Add(singleEquipData.Key);
            }
        }
        ReplacementMainEquip();
        for (int i = 0; i < _unlockList.Count; i++)
        {
            AddEquip(_unlockList[i]);
        }
        _unlockList.Clear();
        ReplacementMainEquip();
        GlobalEvent.DispatchEvent(EGameEvent.EquipUnlock);
    }

    /// <summary>
    /// 替换到正确的主武器
    /// </summary>
    public void ReplacementMainEquip()
    {
        List<int> solt = new();
        foreach (var item in HoleData)
        {
            var cfg = EquipConfig.Instance.Get(item.Value);
            if (
                item.Value > 0
                && cfg.ImportantEquip == 1
                && item.Value != BattleData.Instance.GetUseHreoMainEquipID()
            )
                solt.Add(item.Key);
        }
        foreach (var index in solt)
        {
            RemoveEquip(index);
        }
    }

    public void ClearNewUnlock()
    {
        _newUnlockList.Clear();
    }

    // 移除装备
    public void RemoveEquip(int holeId)
    {
        var equipId = _holeData.GetValue(holeId);
        if (equipId <= 0) // 未解锁 || 本来就为空
        {
            Debug.LogError($"移除装备操作不合法,槽位id:{holeId}");
            return;
        }

        //添加到已解锁的list当中
        _unlockList.Add(equipId);
        _holeData.SetValue(holeId, 0);

        GlobalEvent.DispatchEvent(EGameEvent.HandleEquip);
    }

    // 装备上阵
    public void AddEquip(int equipId)
    {
        // 判断是否有空位
        var inBattleNum = GetInBattleEquipNum();
        var unlockedHoleNum = GetUnlockedHoleNum();
        if (inBattleNum >= unlockedHoleNum)
        {
            Util.FloatTips("装备已满");
            return;
        }

        var keys = _holeData.Get().Keys.ToList();
        foreach (var holdId in keys)
        {
            var holeState = _holeData.GetValue(holdId);
            if (holeState == 0)
            {
                _holeData.SetValue(holdId, equipId);
                break;
            }
        }
        GlobalEvent.DispatchEvent(EGameEvent.HandleEquip);
    }

    // 获得今日装备升级的次数
    public int GetEquipLevelUpCount()
    {
        _dayOfLevel ??= new SerializeDictionary<int, int>(
            Key_DayOfLevelUp,
            new Dictionary<int, int>() { { DateTime.Now.DayOfYear, 0 } }
        );
        if (!_dayOfLevel.ContainsKey(DateTime.Now.DayOfYear)) // 跨天了(重置一下)
        {
            _dayOfLevel.Clear();
            _dayOfLevel = new SerializeDictionary<int, int>(
                Key_DayOfLevelUp,
                new Dictionary<int, int>() { { DateTime.Now.DayOfYear, 0 } }
            );
        }
        var count = _dayOfLevel.GetValue(DateTime.Now.DayOfYear);
        return count;
    }

    public void EquipRise(int equipId)
    {
        var singleEquipData = GetSingleEquipDataByEquipId(equipId);
        var maxLevel = GetEquipMaxLevel(equipId);
        if (singleEquipData.Level >= maxLevel)
            return;

        // 扣除消耗
        var upgradeCfg = GetEquipUpgradeCfgByEquipId(equipId);
        BagData.Instance.UseProp(upgradeCfg.UpgradeCostType[0], upgradeCfg.UpgradeCostNum[0]);
        BagData.Instance.UseProp(upgradeCfg.UpgradeCostType[1], upgradeCfg.UpgradeCostNum[1]);

        singleEquipData = singleEquipData.Rise();
        _equipData.SetValue(equipId, singleEquipData);
        int count = GetEquipLevelUpCount();
        count++;
        _dayOfLevel.SetValue(DateTime.Now.DayOfYear, count);
        GlobalEvent.DispatchEvent(EGameEvent.EquipRise);
    }

    private void OnLevelUpdate(params object[] args)
    {
        CheckHoleData();
        UpdateBagList();
    }

    //-----------------------------------------数据获取-----------------------------------------//
    public int GetHoleStateByHoleId(int holeId)
    {
        if (_holeData.ContainsKey(holeId))
        {
            return _holeData.GetValue(holeId);
        }

        Debug.LogError($"根据槽位id获取槽位状态失败,槽位id: {holeId}");
        return 0;
    }

    // 根据装备id获取操作类型(移除还是添加)
    public EEquipOperateType GetOperateTypeByEquipId(int equipId)
    {
        return _unlockList.Contains(equipId) ? EEquipOperateType.Add : EEquipOperateType.Remove;
    }

    public SingleEquipData GetSingleEquipDataByEquipId(int equipId)
    {
        if (_equipData.ContainsKey(equipId))
        {
            return _equipData.GetValue(equipId);
        }

        Debug.LogError($"_equipData中不存在该装备,装备id:{equipId}");
        return null;
    }

    /// <summary>
    /// 通过消耗品ID获取装备id
    /// </summary>
    /// <param name="costId"></param>
    /// <returns></returns>
    public int GetEquipIdByCostId(int costId)
    {
        return _costEquipMap.GetValueOrDefault(costId);
    }

    public EquipUpgradeCfg GetEquipUpgradeCfgByEquipId(int equipId)
    {
        var singleEquipId = GetSingleEquipDataByEquipId(equipId);
        foreach (var equipUpgradeCfg in EquipUpgradeConfig.Instance.GetList())
        {
            if (equipUpgradeCfg.EquipID == equipId && equipUpgradeCfg.Level == singleEquipId.Level)
            {
                return equipUpgradeCfg;
            }
        }

        Debug.LogError($"根据装备id获取装备升级配置失败,装备id:{equipId}");
        return null;
    }

    public List<EquipUpgradeCfg> GetEquipUpgradeCfgListByEquipId(int equipId)
    {
        var cfgList = new List<EquipUpgradeCfg>();
        var allCfgList = EquipUpgradeConfig.Instance.GetList();
        foreach (var equipUpgradeCfg in allCfgList)
        {
            if (equipUpgradeCfg.EquipID == equipId)
            {
                cfgList.Add(equipUpgradeCfg);
            }
        }

        return cfgList;
    }

    public int GetEquipMaxLevel(int equipId)
    {
        var levelList = new List<int>();
        foreach (var equipUpgradeCfg in EquipUpgradeConfig.Instance.GetList())
        {
            if (equipUpgradeCfg.EquipID == equipId)
            {
                levelList.Add(equipUpgradeCfg.Level);
            }
        }

        return levelList.Count > 0 ? levelList.Max() : 0;
    }

    public Dictionary<int, List<int>> GetEquipAttr(int EquipId)
    {
        var singleEquipData = GetSingleEquipDataByEquipId(EquipId);
        var cfgList = GetEquipUpgradeCfgListByEquipId(EquipId);
        var attrDic = new Dictionary<int, List<int>>();

        foreach (var equipUpgradeCfg in cfgList)
        {
            if (singleEquipData.Level >= equipUpgradeCfg.Level)
            {
                var attrTypeArr = equipUpgradeCfg.UpgradeAttriType;
                var attrValueArr = equipUpgradeCfg.UpgradeAttriNum;
                for (var i = 0; i < attrTypeArr.Length; i++)
                {
                    var valueList = new List<int>();
                    valueList.Add(attrValueArr[i]);
                    attrDic[attrTypeArr[i]] = valueList;
                }
            }
        }

        // 添加英雄升级属性到基础属性中
        var heroRaisingAttr = GetRaisingAttrAdd(EquipId);
        foreach (var attr in heroRaisingAttr)
        {
            if (attrDic.ContainsKey(attr.Key))
            {
                // 如果属性已存在，将英雄升级属性加到基础值上
                var valueList = attrDic[attr.Key];
                valueList[0] += attr.Value;
                attrDic[attr.Key] = valueList;
            }
            else
            {
                // 如果属性不存在，创建新的属性项
                var valueList = new List<int>();
                valueList.Add(attr.Value);
                attrDic[attr.Key] = valueList;
            }
        }

        var nextCfg = GetEquipUpgradeCfgByLevel(EquipId, singleEquipData.Level + 1);
        if (nextCfg != null)
        {
            var addTypeArr = nextCfg.UpgradeAttriType;
            var addValueArr = nextCfg.UpgradeAttriNum;
            for (var i = 0; i < addTypeArr.Length; i++)
            {
                if (attrDic.ContainsKey(addTypeArr[i]))
                {
                    var valueList = attrDic[addTypeArr[i]];
                    var addValue = addValueArr[i] - valueList[0];
                    valueList.Add(addValue);
                    attrDic[addTypeArr[i]] = valueList;
                }
            }
        }

        return attrDic;
    }

    private EquipUpgradeCfg GetEquipUpgradeCfgByLevel(int equipId, int level)
    {
        foreach (var equipUpgradeCfg in EquipUpgradeConfig.Instance.GetList())
        {
            if (equipUpgradeCfg.EquipID == equipId && equipUpgradeCfg.Level == level)
            {
                return equipUpgradeCfg;
            }
        }

        return null;
    }

    public int GetInBattleEquipNum()
    {
        var inBattle = 0;
        foreach (var keyValuePair in _holeData.Get())
        {
            if (keyValuePair.Value > 0)
            {
                inBattle++;
            }
        }

        return inBattle;
    }

    /// <summary>
    /// 检测装备是否在使用中
    /// </summary>
    /// <param name="equipID">装备id</param>
    /// <returns></returns>
    public bool CheckEquipInUse(int equipID)
    {
        if (equipID > 0)
        {
            foreach (var kv in _holeData.Get())
            {
                if (kv.Value == equipID)
                {
                    return true;
                }
            }
        }

        return false;
    }

    /// <summary>
    /// 获得指定装备中装备的配置
    /// </summary>
    /// <param name="equipID">装备id</param>
    /// <returns></returns>
    public List<EquipUpgradeCfg> GetUseEquipUpgradeCfgListByEquipId(int equipId)
    {
        var cfgList = new List<EquipUpgradeCfg>();
        var allCfgList = EquipUpgradeConfig.Instance.GetList();
        if (!CheckEquipInUse(equipId))
            return cfgList;
        var data = GetSingleEquipDataByEquipId(equipId);
        foreach (var equipUpgradeCfg in allCfgList)
        {
            if (equipUpgradeCfg.EquipID == equipId && data.Level >= equipUpgradeCfg.Level)
            {
                cfgList.Add(equipUpgradeCfg);
            }
        }

        return cfgList;
    }

    private int GetUnlockedHoleNum()
    {
        var unlocked = 0;
        foreach (var keyValuePair in _holeData.Get())
        {
            if (keyValuePair.Value >= 0)
            {
                unlocked++;
            }
        }

        return unlocked;
    }

    public bool IsUnlock(int equipId)
    {
        var equipCfg = EquipConfig.Instance.Get(equipId);
        if (equipCfg == null)
            return false;

        var isUnlock = false;
        if (equipCfg.UnlockType == 1) //关卡等级解锁
        {
            isUnlock = LevelData.Instance.CurLevel > equipCfg.Unlock;
        }
        else if (equipCfg.UnlockType == 2) //皮肤解锁
        {
            isUnlock = RaisingCharData.Instance.HeroIsUnlock(equipCfg.Unlock);
        }

        return isUnlock;
    }

    public string GetUnlockDesc(int equipId)
    {
        var equipCfg = EquipConfig.Instance.Get(equipId);
        if (equipCfg == null)
            return string.Empty;
        if (equipCfg.UnlockType == 1) //关卡等级解锁
        {
            return "Common_UnlockDesc";
        }
        else if (equipCfg.UnlockType == 2) //皮肤解锁
        {
            var heroCfg = HeroConfig.Instance.Get(equipCfg.Unlock);
            return UnLockData.Instance.GetUnLockDes(heroCfg.UnlockId);
        }

        return string.Empty;
    }

    public bool CheckHaveEquipCanUp()
    {
        bool isHaveEquip = false;
        foreach (var singleEquipData in _equipData.Get())
        {
            if (IsUnlock(singleEquipData.Key))
            {
                var equipUpgradeCfg = EquipData.Instance.GetEquipUpgradeCfgByEquipId(
                    singleEquipData.Key
                );
                if (equipUpgradeCfg == null)
                    return false;
                var maxLevel = GetEquipMaxLevel(singleEquipData.Key);
                if (equipUpgradeCfg.Level < maxLevel)
                {
                    if (
                        EquipConfig.Instance.Get(singleEquipData.Key).ImportantEquip == 1
                        && BattleData.Instance.GetUseHreoMainEquipID() != singleEquipData.Key // 不是当前的主武器不显示
                    )
                        continue;
                    var chipNum = BagData.Instance.GetPropNum(equipUpgradeCfg.UpgradeCostType[0]);
                    var havaNum = chipNum;
                    var needNum = equipUpgradeCfg.UpgradeCostNum[0];

                    var goldNum = BagData.Instance.GetPropNum((int)ECoinType.Gold);
                    var isEnough =
                        havaNum >= needNum && goldNum >= equipUpgradeCfg.UpgradeCostNum[1];
                    if (isEnough)
                    {
                        isHaveEquip = true;
                        break;
                    }
                }
            }
        }
        return isHaveEquip;
    }

    public Dictionary<int, int> GetRaisingAttrAdd(int equipId)
    {
        // Dictionary<int, int> attrDic = new();
        // var upgradeConfigId = cfgCostSkill.Group * 1000 + level;
        // var cfgEquipUp = EquipUpgradeConfig.Instance.Get(upgradeConfigId);

        // 养成的加成
        var _attrDic = RaisingCharData.Instance.GetRaisingCharAttr(
            BattleData.Instance.UseHeroID,
            EquipConfig.Instance.Get(equipId).EquipType
        );

        // foreach (var item in _attrDic)
        // {
        //     if (attrDic.ContainsKey(item.Key))
        //     {
        //         var value = attrDic[item.Key];
        //         attrDic[item.Key] = value + item.Value;
        //     }
        //     else
        //     {
        //         attrDic.Add(item.Key, item.Value);
        //     }
        // }
        return _attrDic;
    }

    public int GetInBattleEquip()
    {
        int count = 0;
        foreach (var item in HoleData)
        {
            if (item.Value > 0)
                count++;
        }
        return count;
    }
}
