using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using GameCommon;
using Unity.VisualScripting;
using UnityEngine;

public enum EWaveRewardState
{
    /// <summary>
    /// 未完成
    /// </summary>
    Lock,

    /// <summary>
    /// 可领取
    /// </summary>
    Active,

    /// <summary>
    /// 领取过
    /// </summary>
    Claimed,
}

public class WaveRewardState
{
    private int _levelId;
    public Dictionary<int, EWaveRewardState> ClaimedInfo = new Dictionary<int, EWaveRewardState>();
    public int HighestWaveWave; // 最大波次

    public void InitData(int levelId)
    {
        _levelId = levelId;
        HighestWaveWave = 0;
        var levelCfg = LevelConfig.Instance.Get(_levelId);
        if (levelCfg != null)
        {
            foreach (var wave in levelCfg.WaveReawrd)
            {
                ClaimedInfo.Add(wave, EWaveRewardState.Lock);
            }
        }
    }

    public EWaveRewardState GetRewardState(int wave)
    {
        if (!ClaimedInfo.ContainsKey(wave))
        {
            Debug.LogError($"波次奖励数据有误,关卡id:{_levelId},波次:{wave}");
        }

        return ClaimedInfo[wave];
    }

    public WaveRewardState UpdateMaxWave(int wave)
    {
        HighestWaveWave = Mathf.Max(HighestWaveWave, wave);
        return this;
    }

    public WaveRewardState UpdateState(int wave, EWaveRewardState waveRewardState)
    {
        if (ClaimedInfo.ContainsKey(wave))
        {
            ClaimedInfo[wave] = waveRewardState;
        }
        else
        {
            Debug.LogError($"更新关卡波次奖励宝箱状态失败,关卡id: {_levelId},波次: {wave}");
        }

        return this;
    }
}

public class LevelData : DataBase<LevelData>
{
    private const string Key_CurLevel = "LevelData.CurLevel";
    private const string Key_ClaimedInfo = "LevelData.ClaimedInfo";
    private const string Key_Challenge = "LevelData.Challenge";
    private const string Key_GoldChallenge = "LevelData.GoldChallenge";

    private bool _isFaceBoss;
    private Vector2 _bossPos;
    private SerializeInt _curLevel;
    private SerializeDictionary<int, WaveRewardState> _claimedInfo;
    /// <summary>
    /// 每日挑战次数
    /// </summary>
    private SerializeDictionary<int, int> _dayOfChallenge;
    private SerializeDictionary<int, int> _dayGoldOfChallenge;
    public int CurLevel => _curLevel.Get();
    public bool IsFaceBoss => _isFaceBoss;

    public Vector2 BossPos => _bossPos;

    public override void Init()
    {
        base.Init();

        _curLevel = new SerializeInt(Key_CurLevel, 1);
        _claimedInfo = new SerializeDictionary<int, WaveRewardState>(Key_ClaimedInfo);

        InitClaimInfo();
    }

    private void InitClaimInfo()
    {
        foreach (var levelCfg in LevelConfig.Instance.GetList())
        {
            if (!_claimedInfo.ContainsKey(levelCfg.ID))
            {
                var rewardState = new WaveRewardState();
                rewardState.InitData(levelCfg.ID);
                _claimedInfo.SetValue(levelCfg.ID, rewardState);
            }
        }
    }

    // 结算时调用
    public void UpdateLevelData(bool isWin,int levelId,int wave)
    {
#if GRAVITY_WECHAT_GAME_MODE
        SdkManager.Instance.LevelEvent(isWin ? 1 : 3);
#endif
        var levelCfg = LevelConfig.Instance.Get(levelId);
        if (levelCfg == null) return;
        var waveReward = _claimedInfo.GetValue(levelId);
        if (waveReward == null) return;
        
        // 更新最大波次
        waveReward = waveReward.UpdateMaxWave(wave);
        _claimedInfo.SetValue(levelId, waveReward);
        
        // 更新波次奖励
        foreach (var i in levelCfg.WaveReawrd)
        {
            if (wave >= i)
            {
                var state = waveReward.GetRewardState(i);
                if (state == EWaveRewardState.Lock)  // 只更新未解锁的
                {
                    waveReward = waveReward.UpdateState(i, EWaveRewardState.Active);
                    _claimedInfo.SetValue(levelId,waveReward);
                }
            }
        }
        int count = GetChallengCount();
        count++;
        _dayOfChallenge.SetValue(DateTime.Now.DayOfYear, count);

        // 更新关卡数据
        if (isWin && _curLevel.Get() == levelId)
        {
            var nextLevel = _curLevel.Get() + 1;
            var cfgLevel = LevelConfig.Instance.Get(nextLevel);
            if (cfgLevel != null)
            {
                _curLevel.Set(nextLevel);
                GlobalEvent.DispatchEvent(EGameEvent.LevelUpdate);
            }
        }
        GlobalEvent.DispatchEvent(EGameEvent.LevelSettlement);
    }

    // 获得领取今日挑战的次数
    public int GetChallengCount()
    {
        _dayOfChallenge ??= new SerializeDictionary<int, int>(
            Key_Challenge,
            new Dictionary<int, int>() { { DateTime.Now.DayOfYear, 0 } }
        );
        if (!_dayOfChallenge.ContainsKey(DateTime.Now.DayOfYear)) // 跨天了(重置一下)
        {
            _dayOfChallenge.Clear();
            _dayOfChallenge = new SerializeDictionary<int, int>(
                Key_Challenge,
                new Dictionary<int, int>() { { DateTime.Now.DayOfYear, 0 } }
            );
        }
        var count = _dayOfChallenge.GetValue(DateTime.Now.DayOfYear);
        return count;
    }

    // 获得领取今日挑战金币关卡的次数
    public int GetGoldChallengCount()
    {
        _dayGoldOfChallenge ??= new SerializeDictionary<int, int>(
            Key_GoldChallenge,
            new Dictionary<int, int>() { { DateTime.Now.DayOfYear, 0 } }
        );
        if (!_dayGoldOfChallenge.ContainsKey(DateTime.Now.DayOfYear)) // 跨天了(重置一下)
        {
            _dayGoldOfChallenge.Clear();
            _dayGoldOfChallenge = new SerializeDictionary<int, int>(
                Key_GoldChallenge,
                new Dictionary<int, int>() { { DateTime.Now.DayOfYear, 0 } }
            );
        }
        var count = _dayGoldOfChallenge.GetValue(DateTime.Now.DayOfYear);
        return count;
    }

    public void AddGoldChallengCount()
    {
        int count = GetGoldChallengCount();
        count++;
        _dayGoldOfChallenge.SetValue(DateTime.Now.DayOfYear, count);
    }

    // 领取结算波次奖励
    public void ClaimWaveReward(int levelId)
    {
        var waveReward = _claimedInfo.GetValue(levelId);
        if (waveReward != null)
        {
            var listReward = new List<RewardShowItem>();
            var cfgLevel = LevelConfig.Instance.Get(levelId);
            for (var i = 0; i < cfgLevel.WaveReawrd.Length; i++)
            {
                var wave = cfgLevel.WaveReawrd[i];
                if (waveReward.ClaimedInfo.ContainsKey(wave))
                {
                    var state = waveReward.ClaimedInfo[wave];
                    if (state == EWaveRewardState.Active)
                    {
                        waveReward = waveReward.UpdateState(wave, EWaveRewardState.Claimed);
                        listReward.AddRange(GetWaveRewards(levelId, wave));
                    }
                }
            }
            if (listReward.Count > 0)
            {
                _claimedInfo.SetValue(levelId, waveReward);
                // 奖励发放
                RewardManager.GetReward(listReward);
                GlobalEvent.DispatchEvent(EGameEvent.BattleClaimWaveReward);
            }
        }
    }

    //----------------------------------------------数据获取----------------------------------------------//

    public WaveRewardState GetWaveRewardState(int levelId)
    {
        if (_claimedInfo.ContainsKey(levelId))
        {
            return _claimedInfo.GetValue(levelId);
        }

        return null;
    }

    // 根据关卡id,波次获取奖励状态
    public EWaveRewardState GetWaveRewardState(int levelId, int wave)
    {
        var waveReward = _claimedInfo.GetValue(levelId);
        if (waveReward == null)
        {
            Debug.LogError($"获取波次奖励数据失败,关卡id: {levelId}");
        }
        if (!waveReward.ClaimedInfo.ContainsKey(wave))
        {
            Debug.LogError($"获取波次奖励数据失败,波次: {wave}");
        }

        return waveReward.ClaimedInfo[wave];
    }

    public int GetMaxLevel()
    {
        var maxLevel = LevelConfig.Instance.GetList().Last().ID;
        maxLevel = Mathf.Min(maxLevel, CurLevel + 1);
        return maxLevel;
    }

    public float GetWaveRewardProgress(int levelId)
    {
        var levelCfg = LevelConfig.Instance.Get(levelId);
        var claimedInfo = _claimedInfo.GetValue(levelId);
        if (levelCfg == null || claimedInfo == null)
            return 0;
        var maxWave = claimedInfo.HighestWaveWave;
        var progress = 0f;
        for (var i = 0; i < levelCfg.WaveReawrd.Length; i++)
        {
            if (i > 0 && maxWave >= levelCfg.WaveReawrd[i])
            {
                progress += 0.5f;
            }
        }

        return progress;
    }

    public List<RewardShowItem> GetWaveRewards(int levelId, int wave)
    {
        var rewards = new List<RewardShowItem>();
        var levelCfg = LevelConfig.Instance.Get(levelId);
        if (levelCfg == null)
            return rewards;
        var index = Array.IndexOf(levelCfg.WaveReawrd, wave);
        int[] rewardIdArr = null;
        int[] rewardIdValue = null;
        switch (index)
        {
            case 0:
                rewardIdArr = levelCfg.reward1Type;
                rewardIdValue = levelCfg.reward1Value;
                break;
            case 1:
                rewardIdArr = levelCfg.reward2Type;
                rewardIdValue = levelCfg.reward2Value;
                break;
            case 2:
                rewardIdArr = levelCfg.reward3Type;
                rewardIdValue = levelCfg.reward3Value;
                break;
        }

        for (var i = 0; i < rewardIdArr.Length; i++)
        {
            RewardShowItem reward = new RewardShowItem
            {
                PropId = rewardIdArr[i],
                PropNum = rewardIdValue[i],
            };
            rewards.Add(reward);
        }

        return rewards;
    }

    public bool CheckHaveReward(int startLevel = 0, int endLevel = 0)
    {
        var levelCfgList = LevelConfig.Instance.GetList();
        foreach (var cfg in levelCfgList)
        {
            if (startLevel > 0 && cfg.ID < startLevel)
            {
                continue;
            }

            if (endLevel > 0 && cfg.ID > endLevel)
            {
                continue;
            }

            if (cfg.ID <= CurLevel)
            {
                foreach (var wave in cfg.WaveReawrd)
                {
                    if (GetWaveRewardState(cfg.ID, wave) == EWaveRewardState.Active)
                    {
                        return true;
                    }
                }
            }
        }
        return false;
    }

#if GAME_DEBUG
    public void SetLevel(int level)
    {
        var cfg = LevelConfig.Instance.Get(level);
        if (cfg != null)
        {
            _curLevel.Set(level);
            GlobalEvent.DispatchEvent(EGameEvent.LevelUpdate);
        }
    }

    public void SetFaceBoos(bool isOn, Vector2 pos)
    {
        _isFaceBoss = isOn;
        _bossPos = pos;
    }

#endif
}
