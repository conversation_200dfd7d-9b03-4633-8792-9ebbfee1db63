using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using GameCommon;
using TMPro;
using UnityEngine;

/// <summary>
/// 战机升级数据
/// </summary>
public class RaisingCharLevelData : DataBase<RaisingCharLevelData>
{
    /// <summary>
    /// 配置
    /// </summary>
    HeroTalentConfig config => HeroTalentConfig.Instance;
    List<HeroTalentCfg> configList => HeroTalentConfig.Instance.GetList();

    public HeroTalentCfg GetConfigByLevel(int level)
    {
        HeroTalentCfg tmp = null;
        foreach (var cfg in configList)
        {
            if (cfg.Leve == level)
                tmp = config.Get(level);
        }
        return tmp;
    }

    public int[] GetAttrType(int level)
    {
        int[] tmp = null;
        foreach (var cfg in configList)
        {
            if (cfg.Leve == level)
                tmp = config.Get(level).Attribute;
        }
        return tmp;
    }

    public int[] GetAttrValue(int type, int level)
    {
        int[] tmp = null;
        foreach (var cfg in configList)
        {
            if (cfg.Leve == level)
                tmp = config.Get(level).AttributeValue;
        }
        return tmp;
    }

    /// <summary>
    /// 获得消耗
    /// </summary>
    /// <param name="targetLevel"></param>
    /// <returns></returns>
    public List<RewardShowItem> GetLevelCost(int targetLevel)
    {
        List<RewardShowItem> tmp = new();
        for (int i = 0; i < configList.Count; i++)
        {
            var cfg = configList[i];
            if (cfg.Leve == targetLevel)
            {
                for (int k = 0; k < cfg.ConsumeType.Length; k++)
                {
                    RewardShowItem prop = new()
                    {
                        PropId = cfg.ConsumeType[k],
                        PropNum = cfg.ConsumeNum[k],
                    };
                    tmp.Add(prop);
                }
            }
        }
        if (tmp.Count == 0) // 满级了或者配置有问题
        { }
        return tmp;
    }

    Dictionary<int, int> attrTmpDic;
    Dictionary<int, int> attr2TmpDic;

    /// <summary>
    /// 获得等级的总加成
    /// </summary>
    /// <param name="targetLevel"></param>
    /// <returns></returns>
    public Dictionary<int, int> GetLevelAllAttr(int targetLevel)
    {
        attrTmpDic ??= new();
        attrTmpDic.Clear();
        for (int i = 0; i < configList.Count; i++)
        {
            var cfg = configList[i];
            if (cfg.Leve <= targetLevel)
            {
                for (int k = 0; k < cfg.Attribute.Length; k++)
                {
                    if (attrTmpDic.ContainsKey(cfg.Attribute[k]))
                        attrTmpDic[cfg.Attribute[k]] += cfg.AttributeValue[k];
                    else
                        attrTmpDic.Add(cfg.Attribute[k], cfg.AttributeValue[k]);
                }
            }
        }
        return attrTmpDic;
    }

    /// 获得指定类型的总加成
    /// </summary>
    /// <param name="targetLevel"></param>
    /// <returns></returns>
    public Dictionary<int, int> GetLevelAllAttrByType(int targetLevel, int _type)
    {
        attr2TmpDic ??= new();
        attr2TmpDic.Clear();
        for (int i = 0; i < configList.Count; i++)
        {
            var cfg = configList[i];
            if (cfg.Leve <= targetLevel && _type == cfg.EquipType)
            {
                for (int k = 0; k < cfg.Attribute.Length; k++)
                {
                    if (attr2TmpDic.ContainsKey(cfg.Attribute[k]))
                        attr2TmpDic[cfg.Attribute[k]] += cfg.AttributeValue[k];
                    else
                        attr2TmpDic.Add(cfg.Attribute[k], cfg.AttributeValue[k]);
                }
            }
        }
        return attr2TmpDic;
    }

    /// <summary>
    /// 获得目标等级的第一个加成属性
    /// </summary>
    /// <param name="targetLevel"></param>
    /// <returns></returns>
    public Tuple<int, int> GetFirstAttr(int targetLevel)
    {
        Tuple<int, int> result = null;
        for (int i = 0; i < configList.Count; i++)
        {
            var cfg = configList[i];
            if (cfg.Leve == targetLevel)
            {
                result = Tuple.Create(cfg.Attribute[0], cfg.AttributeValue[0]);
            }
        }
        return result;
    }

    /// <summary>
    /// 获得格式化字符
    /// </summary>
    /// <param name="targetLevel"></param>
    /// <returns></returns>
    public string AttrValueFormat(int attrType, int attrValue)
    {
        string value = attrValue.ToString();
        if (AttributeConfig.Instance.Get(attrType).IsPercent)
            value = (attrValue / AttributeConfig.Instance.Get(attrType).Divide).ToString() + "%";
        return value;
    }

    /// 所有武器, 所有防具
}
