using System;
using System.Collections.Generic;
using UnityEngine;

namespace GameCommon
{
    public enum EWindowState
    {
        Init,
        Show,
        Hide,
    }

    public enum EWindowLayer
    {
        Normal,
        Navigation,
        Popup,
        Float,
        Guide,
        Debug
    }

    public class WindowBase : MonoBehaviour
    {
        protected int _curOrder = 0;                                      // 界面canvas的order
        protected int _totalOrder = 1;                                    // 界面总共占canvas多少层
        protected object[] _initArgs;                                     // 打开界面的参数
        protected EWindowState _state = EWindowState.Init;                // 当前界面状态
        protected EWindowLayer _layer = EWindowLayer.Normal;              // 界面所在层级
        protected float _destroyTime;                                     // 销毁时间

        protected const float STANDBY_TIME = 60;

        protected Dictionary<Enum, EventHandle> _dicEvents = new Dictionary<Enum, EventHandle>();
        protected Canvas _canvas;
        protected Animator _animator;

        public string WindowName => gameObject.name;
        public int CurOrder => _curOrder;
        public int MaxOrder => _curOrder + _totalOrder;
        public EWindowState State => _state;
        public EWindowLayer Layer => _layer;
        public float DestroyTime => _destroyTime;



        private void Awake()
        {
            _canvas = GetComponent<Canvas>();
            _animator = GetComponent<Animator>();
            OnInit();
        }

        public void Open(object[] args)
        {
            _initArgs = args;
            _state = EWindowState.Show;

            OnOpen();
        }

        public void Close()
        {
            AnimationClip chip = null;
            if (_animator)
            {
                var allChips = _animator.runtimeAnimatorController.animationClips;
                foreach (var t in allChips)
                {
                    if (t.name.Contains("Out"))
                    {
                        chip = t;
                        break;
                    }
                }
            }
            if (null != chip)
            {
                _animator.Play(chip.name, 0, 0);
                DelayTimer.Add(chip.length, true, OnClose);
            }
            else
            {
                OnClose();
            }
        }

        public void Destroy()
        {
            GameObject.Destroy(gameObject);
        }

        /// <summary>
        /// 当初始化时
        /// </summary>
        protected virtual void OnInit()
        {
            _totalOrder = 1;
            _state = EWindowState.Init;
        }

        /// <summary>
        /// 当打开时
        /// </summary>
        protected virtual void OnOpen()
        {
            gameObject.SetActive(true);
            _state = EWindowState.Show;
            GlobalEvent.DispatchEvent(EGameEvent.WindowOpen, WindowName);
        }

        /// <summary>
        /// 当关闭时
        /// </summary>
        protected virtual void OnClose()
        {
            RemoveAllListener();
            gameObject.SetActive(false);
            _state = EWindowState.Hide;
            _destroyTime = Time.unscaledTime + STANDBY_TIME;
            //Debug.Log($"--------close window: {WindowName}");
            GlobalEvent.DispatchEvent(EGameEvent.WindowClose, WindowName);
        }

        protected void SetDestroyTime(float destroyTime)
        {
            _destroyTime = Time.unscaledTime+destroyTime;
        }

        /// <summary>
        /// 设置SortOrder
        /// </summary>
        /// <param name="layer"></param>
        /// <param name="order"></param>
        public void SetLayer(EWindowLayer layer,int order=-1)
        {
            var rect = (RectTransform)transform;
            rect.localPosition = Vector3.zero;
            rect.localScale = Vector3.one;
            rect.anchorMin = Vector2.zero;
            rect.anchorMax = Vector2.one;
            rect.offsetMin = Vector2.zero;
            rect.offsetMax = Vector2.zero;

            _layer = layer;
            _canvas.sortingLayerName = "Default";
            Util.SetLayer(gameObject,"UI");
            if (-1 == order)
            {
                order = UIManager.Instance.GetMaxOrder(_layer) + 1;
            }
            SetOrder(order);
        }

        /// <summary>
        /// 设置SortOrder
        /// </summary>
        /// <param name="order"></param>
        public void SetOrder(int order)
        {
            _curOrder = order;
            _canvas.overrideSorting = true;
            _canvas.sortingOrder = order;
        }

        /// <summary>
        /// 监听事件
        /// </summary>
        /// <param name="uiEvent">事件类型</param>
        /// <param name="action">事件响应委托</param>
        protected void AddListener(Enum uiEvent, EventHandle action)
        {
            if (!_dicEvents.ContainsKey(uiEvent))
            {
                _dicEvents.Add(uiEvent, action);
                GlobalEvent.AddEvent(uiEvent, action);
            }
        }

        /// <summary>
        /// 解除事件监听
        /// </summary>
        /// <param name="uiEvent">事件类型</param>
        protected void RemoveListener(EGameEvent uiEvent)
        {
            if (_dicEvents.ContainsKey(uiEvent))
            {
                GlobalEvent.RemoveEvent(uiEvent, _dicEvents[uiEvent]);
                _dicEvents.Remove(uiEvent);
            }
        }

        /// <summary>
        /// 解除此对象的所有监听的事件
        /// </summary>
        protected void RemoveAllListener()
        {
            foreach (var kv in _dicEvents)
            {
                GlobalEvent.RemoveEvent(kv.Key, kv.Value);
            }
            _dicEvents.Clear();
        }

        /// <summary>
        /// 获取多语言
        /// </summary>
        /// <param name="key"></param>
        public string GetLocalize(string key, params object[] args)
        {
            return Util.GetLocalize(key, args);
        }

        public T GetComponent<T>(string path)
        {
            return gameObject.transform.Find(path).GetComponent<T>();
        }
        
        public virtual void OnClickClose()
        {
            UIManager.Instance.CloseWindow(WindowName);
            if (!WindowName.Equals("TipWindow"))
            {
                AudioManager.ClickSound();
            }
        }
    }
}