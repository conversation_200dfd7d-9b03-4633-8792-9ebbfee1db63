using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class CustomScrollRect :ScrollRect {

    public GameObject parentScroll;

    private enum Direction
    {
        Horizontal,
        Vertical
    }
    //滑动方向
    private Direction m_Direction = Direction.Horizontal;
    //当前操作方向
    private Direction m_BeginDragDirection = Direction.Horizontal;

	protected override void Awake()
	{
        base.Awake();
       
        m_Direction = this.horizontal ? Direction.Horizontal : Direction.Vertical;
	}


	public override void OnBeginDrag(PointerEventData eventData)
	{
        if(parentScroll){
            m_BeginDragDirection = Mathf.Abs(eventData.delta.x) > Mathf.Abs(eventData.delta.y) ? Direction.Horizontal : Direction.Vertical;
            if(m_BeginDragDirection != m_Direction){
                //当前操作方向不等于滑动方向，将事件传给父对象
                ExecuteEvents.Execute(parentScroll, eventData, ExecuteEvents.beginDragHandler);
                return;
            }
        }

        base.OnBeginDrag(eventData);
	}
	public override void OnDrag(PointerEventData eventData)
	{
        if (parentScroll) {
            if (m_BeginDragDirection != m_Direction){
                //当前操作方向不等于滑动方向，将事件传给父对象
                ExecuteEvents.Execute(parentScroll, eventData, ExecuteEvents.dragHandler);
                return;
            }
        }
        base.OnDrag(eventData);
	}

	public override void OnEndDrag(PointerEventData eventData)
	{
        if (parentScroll){
            if (m_BeginDragDirection != m_Direction){
                //当前操作方向不等于滑动方向，将事件传给父对象
                ExecuteEvents.Execute(parentScroll, eventData, ExecuteEvents.endDragHandler);
                return;
            }
        }
        base.OnEndDrag(eventData);
	}
	
    public override void OnScroll(PointerEventData data)
	{
        if (parentScroll){
            if (m_BeginDragDirection != m_Direction){
                //当前操作方向不等于滑动方向，将事件传给父对象
                ExecuteEvents.Execute(parentScroll, data, ExecuteEvents.scrollHandler);
                return;
            }
        }
        base.OnScroll(data);
	}
}

#if UNITY_EDITOR
[CustomEditor(typeof(CustomScrollRect))]
public class CustomScrollRectInspector : UnityEditor.Editor
{
        
}
#endif
