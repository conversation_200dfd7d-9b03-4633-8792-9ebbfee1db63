using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
#if GRAVITY_WECHAT_GAME_MODE
using WeChatWASM;
#endif

namespace GameCommon
{
    public class SerializeDictionary<K,T>
    {
        private string _name;
        private Dictionary<K, T> _value = new Dictionary<K, T>();

        public int Count => _value.Count;

        public SerializeDictionary(string name,Dictionary<K,T> def=null)
        {
            _name = name;
            
            
#if GRAVITY_WECHAT_GAME_MODE
            var strPre = WX.StorageGetStringSync(_name, "");
#else
           var strPre = PlayerPrefs.GetString(_name, "");
#endif
            
            if (string.IsNullOrEmpty(strPre))
            { 
                Set(def);
            }
            else
            {
                //Debug.Log($"---222-name:{name}-strPre:{strPre}");
                _value = NewLitJson.JsonMapper.ToObject<Dictionary<K, T>>(strPre);
            }
        }

        public Dictionary<K,T> Get()
        {
            return _value;
        }

        public void Set(Dictionary<K,T> newValue)
        {
            if (null == newValue)
            {
                return;
            }
            _value = newValue;

            Save();
        }
        
        public void Save()
        {
            if (_value == null)
            {
                return;
            }

            var strValue = NewLitJson.JsonMapper.ToJson(_value);
#if GRAVITY_WECHAT_GAME_MODE
            WX.StorageSetStringSync(_name, strValue);
#else
            PlayerPrefs.SetString(_name, strValue);
            PlayerPrefs.Save();
#endif
        }

        public bool ContainsKey(K key)
        {
            return _value.ContainsKey(key);
        }
        
        public T GetValue(K key,T def=default)
        {
            if (_value.ContainsKey(key))
            {
                return _value[key];
            }

            return def;
        }

        public void SetValue(K key, T value)
        {
            _value[key] = value;

            Save();
        }
        
        public void Remove(K key)
        {
            if (_value.ContainsKey(key))
            {
                _value.Remove(key);
                Save();
            }
        }

        public void Clear()
        {
            _value.Clear();
            Save();
        }
        
        
        public override string ToString()
        {
            string str = "";
            var keys = _value.Keys.ToList();
            for (int i = 0; i < keys.Count; i++)
            {
                str += $"[{keys[i]}:{_value[keys[i]]}],";
            }
            str = str.TrimEnd(',');
            return $"{_name}:{str}";
        }
    }
}