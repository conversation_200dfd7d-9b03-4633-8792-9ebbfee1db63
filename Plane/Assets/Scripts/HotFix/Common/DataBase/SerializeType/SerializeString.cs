using UnityEngine;
#if GRAVITY_WECHAT_GAME_MODE
using WeChatWASM;
#endif

namespace GameCommon
{
    public class SerializeString
    {
        private string _name;
        private string _value;

        public SerializeString(string name,string def)
        {
            _name = name;
            
#if GRAVITY_WECHAT_GAME_MODE
            _value = WX.StorageGetStringSync(_name, def);
#else
            _value = PlayerPrefs.GetString(_name, def);
#endif
        }

        public string Get()
        {
            return _value;
        }

        public void Set(string newValue)
        {
            _value = newValue;
            
            if (_value == null)
            {
                return;
            }
#if GRAVITY_WECHAT_GAME_MODE
            WX.StorageSetStringSync(_name, _value);
#else
            PlayerPrefs.SetString(_name, _value);
            PlayerPrefs.Save();
#endif
        }
        
        public override string ToString()
        {
            return $"{_name}:{_value}";
        }
    }
}