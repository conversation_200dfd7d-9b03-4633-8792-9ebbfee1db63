using System;
using System.Linq;
using UnityEngine;

public class ConstValue
{
    /// <summary>
    /// 盒子中刷新消耗金币
    /// </summary>
    public static int BoxRefreshCoin = 15;

    /// <summary>
    /// 广告获取铜币数量
    /// </summary>
    public static int RefreshGridNum = 30;

    /// <summary>
    /// 铜币刷新必出格子次数
    /// </summary>
    public static int RefreshTheGrid = 5;

    /// <summary>
    /// 盒子中初始金币
    /// </summary>
    public static int BoxInitCoin = 15;
    /// <summary>
    /// 盒子中刷出额外技能概率百分比(/100)
    /// </summary>
    public static string BoxRefreshExtra = "50,10,10,30";
    /// <summary>
    /// 上阵装备槽位数量
    /// </summary>
    public static int EquipedMaxNum = 8;
    /// <summary>
    /// 战斗最高经验
    /// </summary>
    public static int BattleExpMax = 200;
    /// <summary>
    /// 每波战斗结束生命恢复
    /// </summary>
    public static int HpRecoverPerWave = 10;
    /// <summary>
    /// 广告刷新回血
    /// </summary>
    public static int HpRecoveRBuff = 5;
    /// <summary>
    /// 刷新格子最大次数
    /// </summary>
    public static int hp_box_num = 3;
    /// <summary>
    /// 购买钱的最大次数
    /// </summary>
    public static int hp_money_num = 3;
    /// <summary>
    /// 刷新装备最大次数
    /// </summary>
    public static int hp_equip_num = 3;
    /// <summary>
    /// 刷新已有装备的递增权重
    /// </summary>
    public static string refresh_box_weight = "";
    /// <summary>
    /// 刷新格子的递增权重
    /// </summary>
    public static int grid_refresh_weight = 100;
    /// <summary>
    /// 战斗中最大复活次数
    /// </summary>
    public static int revive_num = 3;
    /// <summary>
    /// 吸血buff触发的内置CD
    /// </summary>
    public static float bloodsuck_cd = 0f;
    /// <summary>
    /// 移动灵敏度的相对距离倍数
    /// </summary>
    public static float sensitivity_multiple = 1f;

    /// <summary>
    /// 重复挑战的关卡限制（往前可挑战的数量）
    /// </summary>
    public static int repeat_challenge_lv = 1;


    /// <summary>
    /// 最大闪避概率/10000
    /// </summary>
    public static int max_dodge = 9000;
    /// <summary>
    /// 最大暴击概率/10000
    /// </summary>
    public static int max_cri_rate = 9000;
    /// <summary>
    /// 最大暴击伤害百分比/10000
    /// </summary>
    public static int max_cri_damage = 30000;
    /// <summary>
    /// 最大攻击力加成百分比/10000
    /// </summary>
    public static int max_atk_add = 20000;
    /// <summary>
    /// 最大防御加成百分比/10000
    /// </summary>
    public static int max_defense_add = 20000;
    /// <summary>
    /// 核弹基础系数
    /// </summary>
    public static int bomb_atk = 200;

    /// <summary>
    /// 回血BUFF系数1
    /// </summary>
    public static int restorative_state_buff_1 = 300;

    /// <summary>
    /// 回血BUFF系数2
    /// </summary>
    public static float restorative_state_buff_2 = 0.1f;

    /// <summary>
    /// 复活给予的buff
    /// </summary>
    public static string revive_buffs = "1";

    /// <summary>
    /// 挂机刷新间隔(秒)
    /// </summary>
    public static int IntervalTime = 60;

    /// <summary>
    /// 领取上限
    /// </summary>
    public static int NumMax = 5;

    /// <summary>
    /// 刷新类型
    /// </summary>
    public static int ResetType = 1;

    /// <summary>
    /// 挂机广告id
    /// </summary>
    public static int DurationGiftAd = 2002;

    /// <summary>
    /// 签到双倍领取广告
    /// </summary>
    public static int SevenDayActivityAd = 2002;
    /// <summary>
    /// 102飞机解锁id
    /// </summary>
    public static int aircraft_102_unlock = 2305;
    /// <summary>
    /// 天赋升级广告
    /// </summary>
    public static int LevelUpADId = 2306;

    /// <summary>
    /// 飞机左右偏恢复时间
    /// </summary>
    public static float drag_animation_time = 0.5f;

    /// <summary>
    /// 飞机左右偏拖动阈值
    /// </summary>
    public static float drag_threshold = 0.2f;

    /// <summary>
    /// 默认上阵武器
    /// </summary>
    public static string DefaultEquip = "10101,10501,10701,11201,11301";

    /// <summary>
    /// 隐私协议地址
    /// </summary>
    public static string PrivacyUrl = "https://sites.google.com/view/hjfgoodgames-privacypolicy";
    /// <summary>
    /// 谷歌商店地址
    /// </summary>
    public static string GoogleStoreUrl = "https://play.google.com/store/apps/details?id=com.onetwo.box";

    /// <summary>
    /// 通用颜色
    /// </summary>
    public static Color[] Colors = new Color[5]
    {
        new Color(0.1843f,0.3804f,0.4392f,1),
        new Color(0.4823f,0.2745f,0.1686f,1),
        new Color(0.3019f,0.3647f,0.1607f,1),
        new Color(0.5333f,0.2588f,0.2431f,1),
        new Color(0.0627f,0.1921f,0.0235f,1)
    };
    public static int GoldLevel_skill_up_num;

    public static int GoldLevel_start_Ad = 2204;

    public static int GoldLevel_refresh_Ad = 2204;

    /// <summary>
    /// 游戏速度随弹幕数量变化
    /// </summary>
    public static (int Count, float TimeScale)[] BulletTimeScale;

    /// <summary>
    /// 飞机血条每格血量
    /// </summary>
    public static (float Hp, float PerGrid)[] HpBarPerGrid;

    public void Init()
    {
        var infos = GetType().GetFields();
        for (int i = 0; i < infos.Length; i++)
        {
            var cfgValue = ConstConfig.Instance.Get(infos[i].Name);
            if (null != cfgValue)
            {
                if (infos[i].FieldType == typeof(string))
                {
                    infos[i].SetValue(this, cfgValue.value_3);
                }
                else if (infos[i].FieldType == typeof(int))
                {
                    infos[i].SetValue(this, cfgValue.value_1);
                }
                else if (infos[i].FieldType == typeof(float))
                {
                    infos[i].SetValue(this, (float)cfgValue.value_1 / cfgValue.value_2);
                }
            }
        }
        
        // 单独处理BulletTimeScale
        var strValue = ConstConfig.Instance.Get(nameof(BulletTimeScale))?.value_3;
        if (!string.IsNullOrEmpty(strValue))
        {
            var segments = strValue.Split('|');
            var bulletTokens = segments[0].Split(',');
            var timeScaleTokens = segments[1].Split(',');
            if (bulletTokens.Length > 0 && bulletTokens.Length == timeScaleTokens.Length)
            {
                BulletTimeScale = new (int Count, float TimeScale)[bulletTokens.Length];
                for (int i = 0; i < bulletTokens.Length; i++)
                {
                    BulletTimeScale[i] = (int.Parse(bulletTokens[i]), int.Parse(timeScaleTokens[i]) / 10000f);
                }
            }
        }

        // 单独处理HpBarPerGrid
        strValue = ConstConfig.Instance.Get(nameof(HpBarPerGrid))?.value_3;
        if (!string.IsNullOrEmpty(strValue))
        {
            HpBarPerGrid = strValue
                .Split('|')
                .Select(t => t.Split(','))
                .Select(t => (float.Parse(t[0]), float.Parse(t[1])))
                .ToArray();
        }
    }
}
