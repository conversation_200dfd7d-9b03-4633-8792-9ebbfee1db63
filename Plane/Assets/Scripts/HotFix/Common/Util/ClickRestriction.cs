using System;
using System.Collections;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

[RequireComponent(typeof(Button))]
public class ClickRestriction : MonoBehaviour,IPointerClickHandler
{
    public float _firstInterval = 1f;  // 前摇时间间隔
    public float _clickInterval = 0.5f;  // 两次点击之间的时间间隔

    private Button _clickMask;
    
    private void Awake()
    {
        _clickMask = GetComponent<Button>();
    }
    
    private void OnEnable()
    {
        StartCoroutine(EnableAfterDelay(_firstInterval));
    }
    
    private IEnumerator EnableAfterDelay(float delay)
    {
        _clickMask.enabled = false;
        yield return new WaitForSecondsRealtime(delay);
        _clickMask.enabled = true;
    }

    public void OnPointerClick(PointerEventData pointerEventData)
    {
        if (gameObject.activeInHierarchy)
        {
            StartCoroutine(EnableAfterDelay(_clickInterval));
        }
    }
    
}
