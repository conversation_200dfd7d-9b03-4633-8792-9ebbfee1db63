
using System;
using UnityEngine;

namespace GameCommon
{
        public class SdkManager : Singleton<SdkManager>
        {
                public string clickid;
#if GRAVITY_BYTEDANCE_GAME_MODE
        public TT tt;
#elif GRAVITY_WECHAT_GAME_MODE
                //public WXSdk wx;
                public Play800Sdk p8;
#endif

                private float _startRecordTime;
                private float _endRecordTime;

                public void Init()
                {
#if GRAVITY_BYTEDANCE_GAME_MODE
            tt = new TT();
            tt.Init();
            tt.LogIn();
#elif GRAVITY_WECHAT_GAME_MODE
                        //wx = new WXSdk();
                        //wx.Init();
                        p8 = new Play800Sdk();
                        p8.Login();
#endif
                        GlobalEvent.AddEvent(EGameEvent.LevelUpdate, OnLevelUpdate);
                        GlobalEvent.AddEvent(EGameEvent.BattleStage, OnBattleStage);
                        GlobalEvent.AddEvent(EGameEvent.AdStage, OnAdStage);
                }


                // 桌面快捷方式
                public void IsShortCutExist(Action<bool> callback)
                {
#if GRAVITY_BYTEDANCE_GAME_MODE
            tt.IsShortCutExist(callback);
#endif
                }
                public void CreateShortcut(Action<bool> callback)
                {
#if GRAVITY_BYTEDANCE_GAME_MODE
            tt.CreateShortcut(callback);
#endif
                }
                // 关注
                public void IsFollow(Action<bool> callback)
                {
#if GRAVITY_BYTEDANCE_GAME_MODE
            tt.IsFollowDouYin(callback);
#endif
                }
                public void Follow()
                {
#if GRAVITY_BYTEDANCE_GAME_MODE
            tt.FollowDouYin();
#endif
                }
                // 收藏
                public bool IsFavorite()
                {
#if GRAVITY_BYTEDANCE_GAME_MODE
            return tt.IsFavorite();
#endif
                        return true;
                }
                public void Favorite(Action<bool> cb)
                {
#if GRAVITY_BYTEDANCE_GAME_MODE
            tt.Favorite(cb);
#endif
                }

                // 用户授权信息界面
                public void OpenSettingsPanel()
                {
#if GRAVITY_BYTEDANCE_GAME_MODE
            tt.OpenSettingsPanel();
#endif
                }

                public void ShowRewardVideoAd(string videoId)
                {
#if GRAVITY_BYTEDANCE_GAME_MODE
            tt.ShowRewardVideoAd(videoId);
#elif GRAVITY_WECHAT_GAME_MODE
                        //wx.ShowRewardVideoAd();
                        p8.VideoAdShow(videoId);
#endif
                }

                public void StopRecord(bool autoMerge = true)
                {
                        _endRecordTime = Time.time;
#if GRAVITY_BYTEDANCE_GAME_MODE
            tt.StopRecord(autoMerge);
#endif
                }

                public void RecordVideo(bool isRecordAudio = true, int maxRecordTime = 600)
                {
                        _startRecordTime = Time.time;
#if GRAVITY_BYTEDANCE_GAME_MODE
            tt.RecordVideo(isRecordAudio, maxRecordTime);
#endif
                }

                public void ShareRecord(string templateId = null)
                {
#if GRAVITY_WECHAT_GAME_MODE
                        //wx.Share();
                        return;
#endif

                        if (_endRecordTime - _startRecordTime < 4f)
                        {
                                Util.FloatTips("录制时间小于3s，分享失败");
                                return;
                        }

#if GRAVITY_BYTEDANCE_GAME_MODE
            tt.ShareRecord(templateId);
#endif
                }



                public void OnShow()
                {
#if GRAVITY_BYTEDANCE_GAME_MODE
            tt.OnShow();
#endif
                }

                public void PreLoadInterstitialAd()
                {
#if GRAVITY_BYTEDANCE_GAME_MODE
            tt.PreLoadInterstitialAd();
#endif
                }
                public void CheckScene(Action<bool> support)
                {
#if GRAVITY_BYTEDANCE_GAME_MODE
            tt.CheckScene(support);
#endif
                }

                /// <summary>
                /// 关卡进出上报:"0" 进入关卡,"1" 通关成功,"3" 通关失败,"4" 中途退出
                /// </summary>
                /// <param name="status"></param>
                public void LevelEvent(int status)
                {
#if GRAVITY_WECHAT_GAME_MODE
                        p8.LevelUpgrade(status);

                        if (status == 1)//通关
                        {
                                p8.UpgradeRecord();
                        }
#endif
                }

                public void OnLogin()
                {
                        GESdk.Instance.Start();
                }

                private void OnLevelUpdate(params object[] args)
                {
                        GESdk.Instance.SetSuperProperties();
                        //     GESdk.Instance.SetUserProperties();
                }

                private void OnBattleStage(params object[] args)
                {
                        GESdk.Instance.SetSuperProperties();
                }

                private void OnAdStage(params object[] args)
                {
                        GESdk.Instance.SetSuperProperties();
                }
        }
}
