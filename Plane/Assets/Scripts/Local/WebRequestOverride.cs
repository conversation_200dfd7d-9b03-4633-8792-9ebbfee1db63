using System;
using System.IO;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.AddressableAssets;

internal class WebRequestOverride : MonoBehaviour
{
    private string curResFolder = "";
    private string newResFolder = "";
    private bool hasNewFolder;
    private void Awake()
    {
        curResFolder = $"/{Application.version}/";
        var filePath = Path.Combine(Application.persistentDataPath,"ResFolder.txt");
        //Debug.Log($"-------ResFolder.txt Path: {filePath}");
        if (File.Exists(filePath))
        {
            newResFolder = File.ReadAllText(filePath);
            hasNewFolder = !string.IsNullOrEmpty(newResFolder);
            //Debug.Log($"-------newResFolder: {newResFolder}");
        }
    }

    //Register to override WebRequests Addressables creates to download
    private void Start()
    {
        Addressables.WebRequestOverride = EditWebRequestURL;
    }

    //Override the url of the WebRequest, the request passed to the method is what would be used as standard by Addressables.
    private void EditWebRequestURL(UnityWebRequest request)
    {
        //Debug.Log($"-------request.url: {request.url}");
        if (hasNewFolder)
        {
            request.url = request.url.Replace(curResFolder, newResFolder);
            //Debug.Log($"-------new request.url: {request.url}");
        }
    }
}