using Editor;
using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEditor.AddressableAssets;
using UnityEditor.AddressableAssets.Build;
using UnityEditor.AddressableAssets.Settings;
using UnityEditor.Android;
using UnityEditor.Build.Content;
using UnityEditor.Build.Reporting;
using UnityEngine;
using UnityEngine.WSA;
using Application = UnityEngine.Application;

/// <summary>
/// 打包工具
/// </summary>
public static class BuildTool
{
    
    private enum EAssetsType
    {
        Local,              //本地资源
        InNet,              //内网资源服
        ReleaseTest,        //正式服测试CDN
        Release,            //正式服CDN
    }
    
    private enum ENetType
    {
        Local,              //本地服
        InNet,              //内网服
        Examine,            //审核服
        Release,            //正式服
    }
   
    [MenuItem("Tools/Android/测试/整包APK",false,10)]
    public static void BuildAndroidLocalDebugApk()
    {
        BuildAndroid(true,false,isDevelop:true,assetType:EAssetsType.Local,netType:ENetType.InNet);
    }
    
    [MenuItem("Tools/Android/测试/更新包APK",false,11)]
    public static void BuildAndroidUpdateDebugApk()
    {
        BuildAndroid(true,false,isDevelop:true,assetType:EAssetsType.InNet,netType:ENetType.InNet);
    }
    
    [MenuItem("Tools/Android/测试/更新包",false,12)]
    public static void BuildAndroidUpdateDebug()
    {
        BuildUpdateAsset(EAssetsType.InNet,ENetType.InNet,true);
    }
    
    
    [MenuItem("Tools/Android/正式/整包APK",false,10)]
    public static void BuildAndroidLocalReleaseApk()
    {
        BuildAndroid(false,false,assetType:EAssetsType.Local,netType:ENetType.InNet);
    }
    //----------------------------------------------------
    
    
    
    // [MenuItem("Tools/Android/外网/整包APK",false,20)]
    // public static void BuildAndroidOutNetTestApk()
    // {
    //     BuildAndroid(true,false,isDevelop:true,assetType:EAssetsType.Local,netType:ENetType.Examine);
    // }
    //
    // [MenuItem("Tools/Android/外网/更新包APK",false,21)]
    // public static void BuildAndroidOutNetTestUpdateApk()
    // {
    //     BuildAndroid(true,false,isDevelop:true,assetType:EAssetsType.ReleaseTest,netType:ENetType.Examine);
    // }
    //
    // [MenuItem("Tools/Android/外网/更新包",false,22)]
    // public static void BuildAndroidOutNetTestUpdate()
    // {
    //     BuildUpdateAsset(EAssetsType.ReleaseTest,ENetType.Examine,true);
    // }
    //
    // //-----------------------------------------
    //
    //
    //
    //
    // [MenuItem("Tools/Android/正式服/APK",false,30)]
    // public static void BuildAndroidReleaseApk()
    // {
    //     BuildAndroid(false,false,assetType:EAssetsType.Local);
    // }
    //
    // [MenuItem("Tools/Android/正式服/AAB",false,31)]
    // public static void BuildAndroidReleaseAAB()
    // {
    //     BuildAndroid(false,true,assetType:EAssetsType.Release);
    // }
    //
    // [MenuItem("Tools/Android/正式服/更新包",false,32)]
    // public static void BuildAndroidUpdateRelease()
    // {
    //     BuildUpdateAsset(EAssetsType.Release,ENetType.Release,false);
    // }
    //
    // //--------------------------------------------------
    //
    //
    //
    // [MenuItem("Tools/Android/内网/测试工程",false,41)]
    // public static void BuildAndroidReleaseDebugProject()
    // {
    //     BuildAndroid(true, isProject: true);
    // }
    //
    // [MenuItem("Tools/Android/正式服/正式工程",false,42)]
    // public static void BuildAndroidReleaseProject()
    // {
    //     BuildAndroid(false, isProject: true);
    // }
    //
    // //-----------------------------------
    //
    //
    //
    //
    //
    // [MenuItem("Tools/iOS/Debug",false,50)]
    // public static void BuildIosDebug()
    // {
    //    
    // }
    //
    // [MenuItem("Tools/iOS/Release",false,51)]
    // public static void BuildIosRelease()
    // {
    //    
    // }
    
    //-----------------------------------
    
    
    
    
    
    [MenuItem("Tools/Window/本地测试包",false,10)]
    public static void BuildWindowDebug()
    {
       BuildWindows();
    }
  
    
    //===============================================================






    private static void BuildAndroid(bool isDebug=false, bool isBundle = true, bool isOpen = true, bool isProject = false,
        bool isDevelop = false, EAssetsType assetType = EAssetsType.Release, ENetType netType = ENetType.Release)
    {
        ExcelToConfig.BuildConfigOutside(false, isSuccess =>
        {
            if (isSuccess)
            {
                EditorUserBuildSettings.SwitchActiveBuildTarget(BuildTargetGroup.Android,BuildTarget.Android);
                BuildTarget target = EditorUserBuildSettings.activeBuildTarget;
                
                var settings = AddressableAssetSettingsDefaultObject.Settings;
                var profileName = "Default";
                
                //var symbols = new string[] {"CT_BWF;"};
                if (assetType == EAssetsType.Local)//本地资源
                {
                    profileName = "Local";
                }
                else if (assetType == EAssetsType.InNet)//内网测试远程
                {
                    profileName = "Test";
                }
                else if (assetType == EAssetsType.Release)//外网正式远程
                {
                    profileName = "Release";
                }
                else if (assetType == EAssetsType.ReleaseTest)//外网测试远程
                {
                    profileName = "ReleaseTest";
                }
                
                var profileId = settings.profileSettings.GetProfileId(profileName);
                settings.activeProfileId = profileId;

               
                var code = PlayerSettings.Android.bundleVersionCode + 1;
                var typeName = isDebug ? "Debug" : "Release";
                var name = Application.productName + $"_v{PlayerSettings.bundleVersion}_" + code + "_" + DateTime.Now.ToString("yyyyMMddHHmm") + "_" + typeName.ToLower();
                var outFolder = $"{Application.dataPath.Replace("/Assets", "/Build/Android/" + typeName)}";
                var outPath = $"{outFolder}/{name}";
                var targetGroup = BuildTargetGroup.Android;
                
                PlayerSettings.SetScriptingBackend(targetGroup, ScriptingImplementation.IL2CPP);
                
                PlayerSettings.Android.targetArchitectures = AndroidArchitecture.ARMv7 | AndroidArchitecture.ARM64;
                PlayerSettings.Android.bundleVersionCode = code;

                if (isProject)
                {
                    EditorUserBuildSettings.exportAsGoogleAndroidProject = true;
                    outFolder = $"{Application.dataPath.Replace("/Assets", "/Build/Android/Project")}";
                    outPath = outFolder;
                    if (Directory.Exists(outPath))
                    {
                        Directory.Delete(outPath,true);
                    }
                    Directory.CreateDirectory(outPath);
                }
                else
                {
                    EditorUserBuildSettings.exportAsGoogleAndroidProject = false;
                    if (isBundle)
                    {
                        EditorUserBuildSettings.buildAppBundle = true;
                        outPath += ".aab";
                    }
                    else
                    {
                        EditorUserBuildSettings.buildAppBundle = false;
                        outPath += ".apk";
                    }
                }

                PlayerSettings.keyaliasPass = "onetwo666&box";
                PlayerSettings.keystorePass = "onetwo666&box";
                
                //设置宏
                var listSymbol = new List<string>();
                if (isDebug)
                {
                    listSymbol.Add("GAME_DEBUG");
                }
                // switch (netType)
                // {
                //     case ENetType.Local:
                //         listSymbol.Add("NET_LOCAL");
                //         break;
                //     case ENetType.InNet:
                //         listSymbol.Add("NET_IN");
                //         break;
                //     case ENetType.Examine:
                //         listSymbol.Add("NET_EXAMINE");
                //         break;
                //     case ENetType.Release:
                //         listSymbol.Add("NET_RELEASE");
                //         break;
                // }
                PlayerSettings.SetScriptingDefineSymbolsForGroup(targetGroup, listSymbol.ToArray());
                UnityEditor.Compilation.CompilationPipeline.RequestScriptCompilation();
                
                
                //整包需要把之前的AA资源包清除
                if (0 != assetType)
                {
                    var aaFolder = $"{Application.dataPath}/../ServerData/Android/{PlayerSettings.bundleVersion}";
                    if (Directory.Exists(aaFolder))
                    {
                        Directory.Delete(aaFolder,true);
                    }
                }
                
                AddressableAssetSettings.CleanPlayerContent();
                AddressableAssetSettings.BuildPlayerContent();//Addressable打包资源API
                AssetDatabase.Refresh();

                var buildPlayerOptions = new BuildPlayerOptions();
                buildPlayerOptions.scenes = new[] { "Assets/Scenes/Game.unity" };
                buildPlayerOptions.locationPathName = outPath;
                buildPlayerOptions.targetGroup = targetGroup;
                buildPlayerOptions.target = target;
                buildPlayerOptions.options = isDevelop ? BuildOptions.Development & BuildOptions.CleanBuildCache : BuildOptions.CleanBuildCache;
    
                BuildReport report = BuildPipeline.BuildPlayer(buildPlayerOptions);
                BuildSummary summary = report.summary;
    
                if (summary.result == BuildResult.Succeeded)
                {
                    Debug.Log("Build succeeded: " + summary.totalSize + " bytes");
    
                    if (isOpen)
                    {
                        Application.OpenURL("file://" + outFolder);
                    }
                }
    
                if (summary.result == BuildResult.Failed)
                {
                    Debug.Log("Build failed");
                }

                // symbols = new string[] { "GAME_DEBUG" };
                // PlayerSettings.SetScriptingDefineSymbolsForGroup(targetGroup, symbols);
                // UnityEditor.Compilation.CompilationPipeline.RequestScriptCompilation();
            }
        });
    }
    

    /// <summary>
    /// 构建更新资源
    /// </summary>
    /// <param name="assetType"></param>
     private static void BuildUpdateAsset(EAssetsType assetType,ENetType netType,bool isDebug)
    {
        ExcelToConfig.BuildConfigOutside(false, isSuccess =>
        {
            if (isSuccess)
            {
                EditorUserBuildSettings.SwitchActiveBuildTarget(BuildTargetGroup.Android,BuildTarget.Android);
                
                var settings = AddressableAssetSettingsDefaultObject.Settings;
                var profileName = "Release";
                if (assetType == EAssetsType.InNet)//内网测试远程
                {
                    profileName = "Test";
                }
                else if (assetType == EAssetsType.Release)//外网正式远程
                {
                    profileName = "Release";
                }
                else if (assetType == EAssetsType.ReleaseTest)//外网测试远程
                {
                    profileName = "RemoteTest";
                }
                
                var profileId = settings.profileSettings.GetProfileId(profileName);
                settings.activeProfileId = profileId;
                settings.BuildRemoteCatalog = true;
                
                var targetGroup = BuildTargetGroup.Android;
                
                var listSymbol = new List<string>(){"CT_BWF"};
                if (isDebug)
                {
                    listSymbol.Add("GAME_DEBUG");
                }
                // switch (netType)
                // {
                //     case ENetType.Local:
                //         listSymbol.Add("NET_LOCAL");
                //         break;
                //     case ENetType.InNet:
                //         listSymbol.Add("NET_IN");
                //         break;
                //     case ENetType.Examine:
                //         listSymbol.Add("NET_EXAMINE");
                //         break;
                //     case ENetType.Release:
                //         listSymbol.Add("NET_RELEASE");
                //         break;
                // }
                PlayerSettings.SetScriptingDefineSymbolsForGroup(targetGroup, listSymbol.ToArray());
                
                UnityEditor.Compilation.CompilationPipeline.RequestScriptCompilation();
                
                
                var path = ContentUpdateScript.GetContentStateDataPath(false);
                var result = ContentUpdateScript.BuildContentUpdate(settings, path);
                if (!string.IsNullOrEmpty(result.Error))
                {
                    Debug.LogError(result.Error);
                }

                var outFolder = $"{Application.dataPath}/../ServerData";
                Application.OpenURL("file://" + outFolder);
            }
        });
    }
    
    
     private static void BuildWindows(bool recover=false)
    {
        if (EditorUserBuildSettings.activeBuildTarget != BuildTarget.StandaloneWindows64)
        {
            EditorUtility.DisplayDialog("提示", "需要切换到Windows平台才能打包！！！", "了解");
            return;
        }
        
        ExcelToConfig.BuildConfigOutside(false, isSuccess =>
        {
            if (isSuccess)
            {
                //EditorUserBuildSettings.SwitchActiveBuildTarget(BuildTargetGroup.Standalone,BuildTarget.StandaloneWindows64);
              
                
                var settings = AddressableAssetSettingsDefaultObject.Settings;
                var profileId = settings.profileSettings.GetProfileId("Local");
                settings.activeProfileId = profileId;
                settings.BuildRemoteCatalog = true;
                
                AssetDatabase.Refresh();
                
                AddressableAssetSettings.CleanPlayerContent();
                AddressableAssetSettings.BuildPlayerContent();//Addressable打包资源API
                
                var resCfgPath = $"{Application.dataPath}/Res/Config";
                var streamCfgPath = $"{Application.dataPath}/StreamingAssets/Config";
                if (Directory.Exists(streamCfgPath))
                {
                    Directory.Delete(streamCfgPath,true);
                }
                Directory.CreateDirectory(streamCfgPath);
                
                var dir = new DirectoryInfo(resCfgPath);
                var allFiles = dir.GetFiles();
                for (int i = allFiles.Length - 1; i >= 0; i--)
                {
                    if (!allFiles[i].FullName.Contains(".meta"))
                    {
                        var toPath = Path.Combine(streamCfgPath, allFiles[i].Name);
                        File.Copy(allFiles[i].FullName, toPath);
                    }
                }
                
                AssetDatabase.Refresh();
    
                var outFolder = $"{Application.dataPath}/../../../Doc/验证版本/client";
                var outPath = $"{outFolder}/{Application.productName}.exe";
                var target = BuildTarget.StandaloneWindows64;
                var targetGroup = BuildTargetGroup.Standalone;
                
                if (Directory.Exists(outFolder))
                {
                    Directory.Delete(outFolder,true);
                }
                Directory.CreateDirectory(outFolder);
                
                var symbols = new string[] {"GAME_DEBUG;GAME_TEST;NET_LOCAL;CT_BWF;"};
                PlayerSettings.SetScriptingDefineSymbolsForGroup(targetGroup, symbols);
                PlayerSettings.SetScriptingBackend(targetGroup, ScriptingImplementation.Mono2x);

                var buildPlayerOptions = new BuildPlayerOptions();
                buildPlayerOptions.scenes = new[] { "Assets/Scenes/Game.unity" };
                buildPlayerOptions.locationPathName = outPath;
                buildPlayerOptions.targetGroup = targetGroup;
                buildPlayerOptions.target = target;
                buildPlayerOptions.options = BuildOptions.Development;
    
                BuildReport report = BuildPipeline.BuildPlayer(buildPlayerOptions);
                BuildSummary summary = report.summary;
    
                if (summary.result == BuildResult.Succeeded)
                {
                    Debug.Log("Build succeeded: " + summary.totalSize + " bytes");
    
                    Application.OpenURL("file://" + outFolder);
                }
    
                if (summary.result == BuildResult.Failed)
                {
                    Debug.Log("Build failed");
                }

                if (recover)
                {
                    EditorUserBuildSettings.SwitchActiveBuildTarget(BuildTargetGroup.Android,BuildTarget.Android);
                }
                
                Directory.Delete(streamCfgPath,true);
                File.Delete(streamCfgPath+".meta");
                AssetDatabase.Refresh();
                // symbols = new string[] { "GAME_DEBUG" };
                // PlayerSettings.SetScriptingDefineSymbolsForGroup(targetGroup, symbols);
                // UnityEditor.Compilation.CompilationPipeline.RequestScriptCompilation();
            }
        });
    }
}
